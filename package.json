{"name": "aorta-ai-platform", "version": "0.1.0", "private": true, "description": "A project named demo", "keywords": "", "author": "anonymous(<EMAIL>)", "repository": "", "scripts": {"analyze": "cross-env BUNDLE_ANALYZE=true oula build", "build": "oula build", "dev": "cross-env oula dev", "postinstall": "oula g tmp", "lint": "oula g tmp && npm run lint:js && npm run lint:style", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx ./src", "lint:style": "stylelint --fix \"src/**/*.less\"", "start": "cross-env oula dev", "start:debug": "cross-env DEBUG=builder oula dev", "tsc": "tsc --noEmit", "eslint:output": "eslint --fix -f html > ./eslint-error.html", "prepare": "husky install && node ./.husky/prepare.js &&  husky install && node ./.husky/prepare.js"}, "dependencies": {"@ant-design/pro-utils": "^1.19.5", "@antv/data-set": "^0.11.0", "@antv/l7": "^2.1.9", "@antv/l7-maps": "^2.1.9", "@antv/l7-react": "^2.1.9", "@aorta/chatui": "^0.5.1-beta.5", "@ht-icons/sprite-ui-react": "^0.3.4", "@ht/advanced-table": "^0.5.5", "@ht/sprite-ui": "^1.3.0-beta.10", "@ht/xlog": "^3.4.0", "@msp/markdown-editor": "^0.28.13", "@types/lodash.debounce": "^4.0.6", "@types/lodash.isequal": "^4.5.5", "@umijs/route-utils": "^1.0.33", "axios": "^1.8.4", "bizcharts": "^3.5.3-beta.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.2.6", "core-decorators": "^0.15.0", "dva": "^2.4.0", "hoist-non-react-statics": "^2.5.0", "lodash": "^4.17.11", "lodash-decorators": "^6.0.0", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lru-cache": "^6.0.0", "mockjs": "^1.0.1-beta3", "moment": "^2.25.3", "number-precision": "^1.6.0", "numeral": "^2.0.6", "nzh": "^1.0.3", "omit.js": "^2.0.2", "prop-types": "^15.5.10", "react": "^16.14.0", "react-color": "^2.19.3", "react-dev-inspector": "^1.1.1", "react-dom": "^16.14.0", "react-fittext": "^1.0.0", "react-helmet-async": "^1.0.4", "react-router-dom": "5.2.0", "umi-request": "^1.0.8", "umi-request-progress": "^1.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@ht/eslint-config-htsc": "^2.0.14", "@ht/umi-plugin-hashprefix": "^0.1.4", "@ht/webpack-plugin-concat-jobid": "^2.0.3", "@modern-js-app/eslint-config": "1.2.4", "@oula/oula": "^1.1.3", "@oula/plugin-xlog": "^1.1.2", "@oula/preset-react-pc": "^3.0.4", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/lru-cache": "^5.1.1", "@types/react": "^17.0.0", "@types/react-color": "^3.0.6", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^5", "carlo": "^0.9.46", "chalk": "^4.0.0", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-webpack": "^0.13.1", "eslint-plugin-eslint-comments": "^3.1.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-markdown": "^2.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "gh-pages": "^3.0.0", "husky": "7.0.4", "jsdom-global": "^3.0.2", "lint-staged": "^11.2.2", "mockjs": "^1.0.1-beta3", "postcss": "^8.4.16", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.7.1", "puppeteer-core": "^7.0.1", "stylelint": "^14.9.1", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^26.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-prettier": "^2.0.0", "typescript": "^5"}, "browserslist": ["chrome >= 87", "edge >= 88", "firefox >= 78", "safari >= 14"], "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "engines": {"node": ">=14.0.0"}, "templateConfig": {"name": "template-pc", "version": "1.0.0"}}