﻿/**
 * !!! 注意: 这里的hash路由，在 umi 编译时会被统一添加上 ./config.ts 中的 hashPrefix 配置项
 * icon：在src/layouts/BasicLayout/getIcon.ts中配置
 */
export default [
  {
    path: '/',
    component: '../layouts/BlankLayout',
    routes: [
      {
        path: '/',
        component: '../layouts/BasicLayout',
        routes: [
          {
            name: '知识库管理',
            icon: 'KnowledgeWarehouseManagement',
            path: '/knowledgeWarehouseManagement',
            routes: [
              {
                name: '文件管理',
                icon: 'FileManagement',
                path: '/knowledgeWarehouseManagement/fileManagement',
                component: './knowledgeWarehouseManagement/fileManagement',
              },
              // hideInMenu生效
              // {
              //   name: '看板',
              //   icon: 'DashboardOutlined',
              //   hideInMenu: true,
              //   path: '/knowledgeWarehouseManagement/dashboard',
              //   component: './dashboard/workplace',
              // },
            ]
          },
          {
            name: '数据反馈',
            icon: 'DataFeedback',
            path: '/dataFeedback',
            component: './dataFeedback',
          },
          {
            name: '会话记录',
            icon: 'ConversationRecord',
            path: '/conversationRecord',
            component: './conversationRecord',
          },
          {
            name: '会话详情',
            hideInMenu: true,
            icon: 'ConversationRecord',
            path: '/conversationDetail',
            component: './conversationDetail',
          },
          // {
          //   path: '/',
          //   redirect: '/dashboard',
          // },
          // {
          //   path: '/dashboard',
          //   name: 'Dashboard',
          //   icon: 'DashboardOutlined',
          //   component: './dashboard/workplace',
          // },
          // {
          //   path: '/list',
          //   icon: 'BorderlessTableOutlined',
          //   name: '列表',
          //   component: './list/table-list',
          // },
          // {
          //   name: '404',
          //   icon: 'WarningOutlined',
          //   path: '/exception',
          //   component: './exception/404',
          // },
          // {
          //   component: './exception/404',
          // },
        ],
      },
    ],
  },
];
