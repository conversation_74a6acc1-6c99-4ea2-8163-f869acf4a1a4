import React from 'react';
import logo from '../../assets/logo.png';
import logoText from '../../assets/logoText.png';
import styles from './index.less';

type LogoAreaProps = {
  collapsed: boolean;
};
const LogoArea: React.FC<LogoAreaProps> = ({ collapsed }) => (
  <div className={styles.logoArea}>
    {collapsed ? (
      <img src={logo} alt="logo" />
    ) : (
      <img src={logoText} alt="logoText" />
    )}
  </div>
);

export default LogoArea;
