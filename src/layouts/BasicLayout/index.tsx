import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';
import type { Dispatch, History } from '@oula/oula';
import { transformRoute, getMatchMenu } from '@umijs/route-utils';
import { Layout, Menu, ConfigProvider, message } from '@ht/sprite-ui';
import zhCN from '@ht/sprite-ui/lib/locale/zh_CN';
import type { ConnectState } from '@/models/connect';

import getLocales from '../../locales';
import BaseSettings from '../../../config/BaseSettings';
import MenuUtil from './MenuUtil';
import type { Route } from './typings';
import LogoArea from './LogoArea';
import HeaderInfo from './HeaderInfo';
import type { IUserInfo } from '@/services/types/api/login/queryEmpInfo';
import type { IMenuListItem } from '@/services/types/api/login/queryMenu';
import type { MenuDataItem } from '@umijs/route-utils';
import styles from './index.less';

const { Header, Content, Sider } = Layout;

type BaiscLayoutProps = {
  collapsed: boolean;
  route?: Route;
  dispatch: Dispatch;
  location?: {
    pathname?: string;
  };
  userInfo: IUserInfo;
  customBreadcrumb: MenuDataItem[];
  history: History;
  menuList: IMenuListItem[];
};

const BaiscLayout: React.FC<BaiscLayoutProps> = (props) => {
  const { collapsed, dispatch, route, location, userInfo, customBreadcrumb, history, menuList } = props;

  const formatMessage = ({ id, defaultMessage }: { id: string; defaultMessage?: string; }): string => {
    const locales = getLocales();
    return locales[id] ? locales[id] : defaultMessage as string;
  };

  const { menuData } = transformRoute(
    route?.routes || [],
    // menu?.locale || false,
    false,
    formatMessage,
    true,
  );

  const [menuUtils] = useState(() => new MenuUtil(props));
  const [activeMenu, setActiveMenu] = useState<MenuDataItem[]>([]);
  const [newMenuData, setNewMenuData] = useState<MenuDataItem[]>([]);

  const onCollapse = (payload: boolean): void => {
    if (dispatch) {
      dispatch({
        type: 'global/changeLayoutCollapsed',
        payload,
      });
    }
  };

  useEffect(() => {
    // 查询用户信息
    dispatch({
      type: 'global/queryUserInfo',
    }).then((res: any) => {
      if (res?.code !== '0') {
        message.error(res?.msg);
      } else {
        // 租户id【tenantId】默认取租户信息第一条数据的tenantId（聊TA）
        const tenantId = res?.resultData?.tenantInfo?.[0]?.tenantId;
        // 查询菜单
        dispatch({
          type: 'global/queryMenu',
          payload: { tenantId },
        });
        tenantChange(tenantId);
      }
    });
  }, []);

  // 切换租户，更新租户id【tenantId】
  const tenantChange = (tenantId: string) => {
    dispatch({
      type: 'global/changeTenantId',
      payload: tenantId,
    });
  };

  useEffect(() => {
    // 优先应用自定义的面包屑(customBreadcrumb)，否则自动根据当前选中的菜单匹配生成
    if (_.isEmpty(customBreadcrumb)) {
      setActiveMenu(_.uniqBy(getMatchMenu(location?.pathname || '/', newMenuData, true), 'key'));
    } else {
      setActiveMenu(customBreadcrumb);
    }
  }, [customBreadcrumb, location, newMenuData]);

  // 循环menuData，过滤掉不在pathArray中的菜单数据
  const getMenu = (list: MenuDataItem[], pathArray: string[]) => {
    return _.map(list, item => {
      if (item.path && item.path !== '/' && !item.hideInMenu) {
        if (_.includes(pathArray, item.path)) {
          if (item.children) {
            item.children = getMenu(item.children, pathArray);
            return item;
          }
          return item;
        } else {
          item.hideInMenu = true;
          return item;
        }
      } else {
        if (item.children) {
          item.children = getMenu(item.children, pathArray);
          return item;
        }
        return item;
      }
    });
  };

  useEffect(() => {
    const pathArray: string[] = [];
    // 获取菜单path数组
    const menuListMap = (list: any[]) => {
      _.map(list, item => {
        pathArray.push(item.path);
        if (item.children) {
          menuListMap(item.children);
        }
      });
    };
    menuListMap(menuList);
    const menuDataList = _.cloneDeep(menuData);
    // 过滤菜单
    setNewMenuData(getMenu(menuDataList, pathArray));
  }, [menuList]);

  return (
    <ConfigProvider prefixCls={BaseSettings.appName} locale={zhCN}>
      <Layout style={{ height: '100vh' }}>
        <Sider collapsible={true} collapsed={collapsed} onCollapse={onCollapse}>
          <Header className={styles.siderHeader}>
            <LogoArea collapsed={collapsed} />
          </Header>
          <Menu theme="dark" mode="inline">
            {menuUtils.getNavMenuItems(newMenuData)}
          </Menu>
        </Sider>
        <Layout className={styles.mainLayoutStyle}>
          <Header className={styles.headerStyle}>
            <HeaderInfo
              activeMenu={activeMenu}
              userInfo={userInfo}
              history={history}
              isCustomBreadcrumb={!_.isEmpty(customBreadcrumb)}
              tenantChange={tenantChange}
            />
          </Header>
          <Content className={styles.contentInfo}>
            <div className={styles.mainContent}>{props.children}</div>
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default connect(({ global }: ConnectState) => ({
  customBreadcrumb: global.customBreadcrumb,
  collapsed: global.collapsed,
  userInfo: global.userInfo,
  menuList: global.menuList,
}))(BaiscLayout);
