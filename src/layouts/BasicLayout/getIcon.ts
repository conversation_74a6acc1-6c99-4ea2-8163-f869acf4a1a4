import {
  DashboardOutlined,
  FormOutlined,
  BorderlessTableOutlined,
  ProfileOutlined,
  WarningOutlined,
  FolderOutlined,
  BoxOutlined,
} from '@ht-icons/sprite-ui-react';
import { ReactComponent as KnowledgeWarehouseManagement } from './images/knowledgeWarehouseManagement.svg';
import { ReactComponent as FileManagement } from './images/fileManagement.svg';
import { ReactComponent as DataFeedback } from './images/dataFeedback.svg';
import { ReactComponent as ConversationRecord } from './images/conversationRecord.svg';

const icons = {
  DashboardOutlined,
  WarningOutlined,
  FormOutlined,
  BorderlessTableOutlined,
  ProfileOutlined,
  FolderOutlined,
  BoxOutlined,
  // 知识库管理Icon
  KnowledgeWarehouseManagement,
  // 文件管理Icon
  FileManagement,
  // 数据反馈Icon
  DataFeedback,
  // 会话记录Icon
  ConversationRecord,
};
export default function (name?: string) {
  return !name ? null : icons[name];
}
