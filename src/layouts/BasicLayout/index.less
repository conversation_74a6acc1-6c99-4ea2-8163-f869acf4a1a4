.logoArea {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  padding: 20px;
  img {
    width: 100%;
  }
}

.siderHeader {
  padding: 0;
}

.mainLayoutStyle {
  background: #edf0f7;
  overflow-y: scroll;
}

.headerStyle {
  background: #edf0f7;
  padding: 0 20px 0 26px;
}

.contentInfo {
  height: auto;
}

.mainContent {
  background: #fff;
  border-radius: 24px 24px 0 0;
  min-width: 1050px;
}

.headerInfo {
  .breadcrumb {
    float: left;
    font-size: 14px;

    span {
      .breadcrumbName {
        color: #999ba0;
      }

      .splitLine {
        margin: 0 10px 0 6px;
        color: #999ba0;
      }

      &:last-child {
        .breadcrumbName {
          color: #6c6f76;
        }

        .splitLine {
          display: none;
        }
      }
    }
  }

  .rightInfo {
    display: flex;
    float: right;

    .tenantInfo {
      font-size: 14px;
      color: #2c2d2f;
      margin-right: 32px;
    }

    .userInfo {
      font-size: 12px;
      color: #1b1f32;

      img {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        border-radius: 50%;
      }
    }
  }
}
