import React from 'react';
import _ from 'lodash';
import BaseSettings from '@config/BaseSettings';
import type { History } from '@oula/oula';
import type { MenuDataItem } from './typings';
import { Select } from '@ht/sprite-ui';
import type { IUserInfo } from '@/services/types/api/login/queryEmpInfo';
import styles from './index.less';

const { Option } = Select;

type HeaderInfoProps = {
  activeMenu: MenuDataItem[];
  userInfo: IUserInfo;
  history: History;
  // 是否自定义面包屑
  isCustomBreadcrumb: boolean;
  tenantChange: (tenantId: string) => void;
};
const HeaderInfo: React.FC<HeaderInfoProps> = ({
  activeMenu,
  userInfo,
  history,
  isCustomBreadcrumb,
  tenantChange,
}) => {

  // 面包屑跳转
  const handleMenuClick = (item: MenuDataItem) => {
    let pathInfo = item?.path;
    if (isCustomBreadcrumb) {
      // 自定义面包屑（有path则支持跳转，无path则不跳转）
      if (pathInfo) {
        // 判断path是否以appName开头，如果不是则补全
        if (!_.includes(pathInfo, BaseSettings.appName)) {
          pathInfo = `/${BaseSettings.appName}${pathInfo}`;
        }
        history.push(pathInfo);
      }
    } else if (pathInfo && item.component && item.children) {
      // 自动生成的面包屑
      // 支持跳转的面包屑必须有(path)，有页面(component)，并且有子菜单(children)(非叶子菜单)
      history.push(pathInfo);
    }
  };

  const OptionRender = () => {
    return (
      _.map(userInfo?.tenantInfo, item => (
        <Option key={item.tenantId} value={item.tenantId}>{item.tenantName}</Option>
      ))
    );
  };
  return (
    <div className={styles.headerInfo}>
      <div className={styles.breadcrumb}>
        {_.map(activeMenu, (item) => (
          <span key={item.key || item.name}>
            <span
              className={styles.breadcrumbName}
              onClick={() => handleMenuClick(item)}
            >
              {item.name}
            </span>
            <span className={styles.splitLine}>/</span>
          </span>
        ))}
      </div>
      <div className={styles.rightInfo}>
        <div className={styles.tenantInfo}>
          <span>租户：</span>
          {/* 后期支持切换租户，需添加 onChange 事件，重新查询文件列表和文件类目 */}
          {!_.isEmpty(userInfo?.tenantInfo) && !_.isEmpty(userInfo?.tenantInfo?.[0]?.tenantId) && (
            <Select
              defaultValue={userInfo?.tenantInfo?.[0]?.tenantId}
              allowClear={false}
              bordered={false}
              onChange={tenantChange}
            >
              {OptionRender()}
            </Select>
          )}
        </div>
        <div className={styles.userInfo}>
          <img src={userInfo?.empInfo?.empImgUrl} alt="" />
          <span>{userInfo?.empInfo?.empName}</span>
        </div>
      </div>
    </div>
  );
};

export default HeaderInfo;
