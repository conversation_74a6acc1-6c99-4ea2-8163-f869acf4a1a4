import request from '@/utils/request';
import type { IQueryUserInfo } from './types/api/login/queryEmpInfo';
import type { IGetTenantsInfo } from './types/api/login/getTenantsInfo';
import type { IQueryMenuInfo } from './types/api/login/queryMenu';

/** 获取用户信息 */
export async function queryUserInfo(data: IQueryUserInfo['Query']): Promise<IQueryUserInfo['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/emp/manageHome/pc/empinfo/queryEmpInfo', { data });
}

/** 获取租户列表 */
export async function getTenantsInfo(data: IGetTenantsInfo['Query']): Promise<IGetTenantsInfo['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/tenant/getTenantsInfo', { data });
}

/** 获取菜单 */
export async function queryMenu(data: IQueryMenuInfo['Query']): Promise<IQueryMenuInfo['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/emp/manageHome/pc/menu/queryMenu', { data });
}
