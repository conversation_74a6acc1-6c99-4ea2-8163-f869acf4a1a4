/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-len */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-15
 * @Description：数据反馈相关services
 */
import type { IQueryFeedbackPerson } from './types/api/dataFeedback/queryFeedbackPerson';
import type { IQueryDepartmentInfo } from './types/api/dataFeedback/queryDepartmentInfo';
import type { IQueryFeedbackTagsAndAgentDict } from './types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type { IQueryDataFeedbackList } from './types/api/dataFeedback/queryDataFeedbackList';
import type { IQueryConversationInfo } from './types/api/dataFeedback/queryConversationInfo';
import type { IQueryConversationDetails, ISubmitOperationAnalysis } from './types/api/dataFeedback/queryConversationDetails';

import request from '@/utils/request';

/** 查询反馈人 */
export async function queryFeedbackPerson(data: IQueryFeedbackPerson['Query']): Promise<IQueryFeedbackPerson['Response']> {
  return request.post('/fspa/aorta/user/api/desktop/emp/info/all/empinfo/searchByKeyword', { data });
}

/** 查询所属部门 */
export async function queryDepartmentInfo(data: IQueryDepartmentInfo['Query']): Promise<IQueryDepartmentInfo['Response']> {
  return request.post('/fspa/aorta/user/api/desktop/emp/org/all/orgTree/queryOrgTreesByHQ', { data });
}

/** 查询反馈标签和Agent字典 */
export async function queryFeedbackTagsAndAgentDict(): Promise<IQueryFeedbackTagsAndAgentDict['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/feedback/queryDict');
}

/** 查询数据反馈列表 */
export async function queryDataFeedbackList(data: IQueryDataFeedbackList['Query']): Promise<IQueryDataFeedbackList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/feedback/queryDataFeedbackList', { data });
}

/** 查询会话信息 */
export async function queryConversationInfo(data: IQueryConversationInfo['Query']): Promise<IQueryConversationInfo['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/queryConversationInfo', { data });
}

/** 查询会话详情 */
export async function queryConversationDetails(data: IQueryConversationDetails['Query']): Promise<IQueryConversationDetails['Response']> {
  return request.post('/fspa/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryUserHistoryMessagesInConversation', { data });
}

/** 提交运营分析 */
export async function submitOperationAnalysis(data: ISubmitOperationAnalysis['Query']): Promise<ISubmitOperationAnalysis['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/feedback/submitAnalysis', { data });
}
