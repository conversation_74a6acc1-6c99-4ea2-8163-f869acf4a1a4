/*
 * <AUTHOR> sun<PERSON><PERSON><K0100008>
 * @Date         : 2025-04-14
 * @Description  : 通用的 api 接口相关 TS 定义
 */
interface Response<T> {
  /** 接口Code, 0表示成功 */
  code: string;
  /** 接口信息 */
  msg: string;
  /** 接口消息类型 */
  messageType: string;
  /** 接口后端的traceId */
  traceId: string;
  /** 接口返回数据 */
  resultData: Nillish<T> | boolean;
}

/** 类型可能为 null | undefined  */
export type Nillish<T> = T | null | undefined;

/** API 相应类型 */
export type ApiResponse<T> = Nillish<Response<T>>;
