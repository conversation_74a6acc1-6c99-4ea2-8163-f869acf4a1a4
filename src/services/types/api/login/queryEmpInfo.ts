import type { ApiResponse } from '../common';

/** 租户信息Item */
export interface ITenantListItm {
  /** 租户名称 */
  tenantName: string;
  /** 租户id */
  tenantId: string;
}

/** 用户信息 */
export interface IEmpInfo {
  /** 工号 */
  empNum: string;
  /** 姓名 */
  empName: string;
  /** 头像 */
  empImgUrl: string;
}

/** 角色权限信息 */
export interface IRoleInfo {
  /** 数据运营权限 */
  admin: boolean;
  /** 编辑知识库权限 */
  edit: boolean;
  /** 查看知识库权限 */
  view: boolean;
}

/** 用户信息 */
export interface IUserInfo {
  /** 租户信息 */
  tenantInfo: ITenantListItm[];
  /** 用户信息 */
  empInfo: IEmpInfo;
  /** 角色权限信息 */
  roleInfo: IRoleInfo;
}

/** 获取用户信息-接口TS */
export interface IQueryUserInfo {
  Query: {
    /** empId */
    empId: string;
  };
  Response: ApiResponse<IUserInfo>;
}
