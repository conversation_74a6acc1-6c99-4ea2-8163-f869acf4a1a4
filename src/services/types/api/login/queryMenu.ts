import type { ApiResponse } from '../common';

/** 菜单列表Item */
export interface IMenuListItem {
  id: string;
  name: string;
  pid: string;
  path: string;
  type: string;
  order: number;
  children: IMenuListItem[];
}

/** 获取菜单结果 */
export interface IQueryMenuResult {
  /** 菜单列表 */
  menuList: IMenuListItem[];
}

/** 获取菜单-接口TS */
export interface IQueryMenuInfo {
  Query: {
    /** 租户id */
    tenantId: string;
  };
  Response: ApiResponse<IQueryMenuResult>;
}
