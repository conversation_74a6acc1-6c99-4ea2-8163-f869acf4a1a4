import type { ApiResponse } from '../common';
import type { StatusCodeType } from '@/components/FileAnalysis/config';

export interface IFileAnalysisResult {
  /** 文档ID */
  fileId: string;
  /** 文件解析结果 */
  analysisResult: StatusCodeType;
}

/** 获取文件的解析结果 */
export interface IGetFileAnalysisResult {
  Query: {
    /** 文档列表 */
    fileIdList: string[];
  };
  Response: ApiResponse<IFileAnalysisResult[]>;
}
