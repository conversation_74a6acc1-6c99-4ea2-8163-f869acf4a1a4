import type { ApiResponse } from '../common';

export interface ISaveChunkResult {
  /** 当前文档分片 */
  chunkId: string;
}

/** 保存文档分片 */
export interface ISaveChunk {
  Query: {
    /** 当前文档ID */
    fileId: string | null;
    /** 当前文档分片ID */
    chunkId: string | null;
    /** 文档分片内容 */
    chunkContent: string;
    /** 文档分片摘要 */
    chunkSummary: string;
    /** 当前分片前的chunkId */
    prevChunkId: string | null;
    /** 当前分片后的chunkId */
    nextChunkId: string | null;
    /** 租户id */
    tenantId: string;
  };
  Response: ApiResponse<ISaveChunkResult>;
}
