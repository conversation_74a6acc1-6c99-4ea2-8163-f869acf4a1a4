import type { ApiResponse } from '../common';

import type { StatusCodeType } from '@/components/FileAnalysis/config';

export interface IFileChunkProcessResult {
  /** 文档ID */
  fileId: string;
  /** 文件分片结果 */
  chunkProcess: StatusCodeType;
}

/** 获取文件分片进程接口 */
export interface IGetFileChunkProcess {
  Query: {
    /** 文档列表 */
    fileIdList: string[];
  };
  Response: ApiResponse<IFileChunkProcessResult[]>;
}
