import type { ApiResponse } from '../common';

export interface IRecallTestResult {
  /** 序号 */
  orderNum: number;
  /** 相似度值 */
  similarityValue: number;
  /** 切片内容 */
  content: string;
  /** 文档名称 */
  fileName: string;
  /** 文档ID */
  fileId: string;
  /** 分片序号 */
  chunkOrder: string;
}

/** 召回测试的接口请求 */
export interface IRecallTest {
  Query: {
    /** 文档ID列表 */
    fileIdList: string[];
    /** 相似度阈值 */
    thresholdValue: number | null;
    /** 输入文档关键字 */
    keyword: string;
  };
  Response: ApiResponse<IRecallTestResult[]>;
}
