import type { ApiResponse } from '../common';

/** 文档切片信息 */
export interface IFileChunkInfo {
  /** 切片ID */
  chunkId: string;
  /** 切片内容 */
  chunkContent: string;
  /** 文档分片摘要 */
  chunkSummary: string | null;
  /** 文档是否结尾 */
  conclusion: boolean;
  /** 文档是否已经校验 */
  checkoutFlag: boolean;
}

/** 获取文档切片列表-接口TS */
export interface IGetMarkDownChunks {
  Query: {
    /** 文档ID */
    fileId: string;
  };
  Response: ApiResponse<IFileChunkInfo[]>;
}
