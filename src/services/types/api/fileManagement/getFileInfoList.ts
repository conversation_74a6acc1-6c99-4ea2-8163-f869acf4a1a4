import type { ApiResponse } from '../common';

export type FileType = Readonly<{
  /** 未开始 */
  PDF: 'pdf';
  /** 处理中 */
  HTML: 'html';
}>;

/** 获取文档解析等页面需要的文档信息DTO */
export interface IFileInfo {
  /** 文件ID */
  fileId: string;
  /** 文件名称 */
  fileName: string;
  /** 文件类型 */
  fileType: FileType[keyof FileType];
  /** 源文档文件地址 */
  originFileUrl: string;
}

/** 获取文档解析等页面需要的文档信息 */
export interface IGetFileInfoList {
  Query: {
    /** 文档列表 */
    fileIdList: string[];
  };
  Response: ApiResponse<IFileInfo[]>;
}
