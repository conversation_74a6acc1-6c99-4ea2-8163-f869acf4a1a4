import type { ApiResponse } from '../common';

/** 是否启用枚举映射 */
export type EnabledObj = Readonly<{
  Y: '是',
  N: '否',
}>;

export type DataType = {
  label: string | null;
  value: string | number | null;
}

/** 文件管理列表Item */
export interface FileManagementListItem {
  /** 文件id */
  fileId: string;
  /** 类目和文档的关联关系id */
  relationId: string;
  /** 文件名称 */
  fileName: string;
  /** 文件格式 */
  fileFormat: string;
  /** 版本 */
  version: string;
  /** 类目 */
  categoryInfo: DataType[];
  /** 数据来源 */
  dataSources: DataType;
  /** 更新时间 */
  updateTime: string;
  /** 是否启用 */
  /** 是：Y，否：N */
  // enabled: EnabledObj[keyof EnabledObj];
  enabled: string;
  /** 当前状态 */
  fileStatus: DataType;
  /** 切片校验状态 */
  chunkVerifyStatus: DataType;
  /** 列表ID */
  id: number;
}

/** 文件管理列表分页数据 */
export interface FileManagementListPage {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总条数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
}

/** 文件管理列表 */
export interface FileManagementList {
  /** 文件列表数据 */
  list: FileManagementListItem[];
  /** 分页数据 */
  page: FileManagementListPage;
}

/** 查询文件管理列表-接口TS */
export interface IQueryFileManagementList {
  Query: {
    /** 租户id */
    tenantId: string;
    /** 文件名称 */
    fileName: string;
    /** 类目 */
    categoryId: string[];
    /** 来源 */
    dataSourceId: string[];
    /** 页码 */
    pageNum: number;
    /** 每页大小 */
    pageSize: number;
  };
  Response: ApiResponse<FileManagementList>;
}
