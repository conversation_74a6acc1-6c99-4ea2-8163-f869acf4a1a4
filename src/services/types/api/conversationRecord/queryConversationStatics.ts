import type { ApiResponse } from '../common';

/** 统计信息 */
export interface ConversationStaticsResult {
  /** 会话数 */
  conversationNum: number;
  /** 问题数 */
  questionNum: number;
  /** 子问题数 */
  subQuestionNum: number;
  /** 提问人 */
  questionerNum: number;
  /** 点赞数 */
  likesNum: number;
  /** 点踩数 */
  dislikesNum: number;
}

/** 查询会话维度的统计信息-接口TS */
export interface IQueryConversationStatics {
  Query: {
    /** 首次提问开始时间 */
    startTime: string;
    /** 首次提问结束时间 */
    endTime: string;
    /** 提问人 */
    empId: string;
    /** 所属部门 */
    orgIdList: string[];
  };
  Response: ApiResponse<ConversationStaticsResult>;
}
