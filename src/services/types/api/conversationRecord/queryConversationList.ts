import type { ApiResponse } from '../common';

/** 会话维度列表Item */
export interface ConversationListItem {
  /** 会话Id */
  conversationId: string;
  /** 提问人 */
  questioner: string;
  /** 所属分支部门 */
  department: string;
  /** 首次提问时间 */
  questionTime: string;
  /** 问题数 */
  questionNum: number;
  /** 点赞数 */
  likesNum: number;
  /** 点踩数 */
  dislikesNum: number;
}

/** 列表分页数据 */
export interface ListPage {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总条数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
}

/** 会话维度列表 */
export interface ConversationList {
  /** 会话维度列表数据 */
  list: ConversationListItem[];
  /** 分页数据 */
  page: ListPage;
}

/** 查询会话维度列表-接口TS */
export interface IQueryConversationList {
  Query: {
    /** 首次提问开始时间 */
    startTime: string;
    /** 首次提问结束时间 */
    endTime: string;
    /** 提问人 */
    empId: string;
    /** 所属部门 */
    orgIdList: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 排序字段 */
    sortField: string;
    /** 排序顺序 */
    sortOrder?: string | null;
  };
  Response: ApiResponse<ConversationList>;
}
