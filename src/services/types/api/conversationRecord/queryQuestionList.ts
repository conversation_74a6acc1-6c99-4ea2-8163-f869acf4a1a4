import type { ApiResponse } from '../common';

export type AnswerFeedbackType = {
  /** code */
  code: string;
  /** 名称 */
  name: string;
}

/** 问题维度列表Item */
export interface QuestionListItem {
  id: string;
  messageId?: string;
  /** 会话Id */
  conversationId: string;
  /** traceID */
  traceId: string;
  /** 提问人 */
  questioner: string;
  /** 所属分支部门 */
  department: string;
  /** 问题详情 */
  questionDetail: string;
  /** 提问时间 */
  questionTime: string;
  /** 答案反馈 */
  answerFeedback: AnswerFeedbackType;
}

/** 列表分页数据 */
export interface ListPage {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总条数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
}

/** 问题维度列表 */
export interface QuestionList {
  /** 问题维度列表数据 */
  list: QuestionListItem[];
  /** 分页数据 */
  page: ListPage;
}

/** 查询问题维度列表-接口TS */
export interface IQueryQuestionList {
  Query: {
    /** 首次提问开始时间 */
    startTime: string;
    /** 首次提问结束时间 */
    endTime: string;
    /** 提问人 */
    empId: string;
    /** 所属部门 */
    orgIdList: string[];
    /** 答案反馈 */
    answerFeedback: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 排序字段 */
    sortField: string;
    /** 排序顺序 */
    sortOrder?: string | null;
  };
  Response: ApiResponse<QuestionList>;
}
/** 查询问题维度列表-接口TS */
export interface IQuerySubQuestionList {
  Query: {
    /** 首次提问开始时间 */
    startTime: string;
    /** 首次提问结束时间 */
    endTime: string;
    /** 提问人 */
    empId: string;
    /** 所属部门 */
    orgIdList: string[];
    /** 答案来源 */
    answersSources: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 排序字段 */
    sortField: string;
    /** 排序顺序 */
    sortOrder?: string | null;
  };
  Response: ApiResponse<QuestionList>;
}
