import type { ApiResponse } from '../common';

export type DataListType = {
  /** 工号 */
  userId: string;
  /** 姓名 */
  userName: string;
  /** 状态 */
  userStatus: string;
}

/** 反馈人信息 */
export interface FeedbackPersonResult {
  /** 员工列表 */
  dataList: DataListType[];
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总条数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
}

/** 查询反馈人-接口TS */
export interface IQueryFeedbackPerson {
  Query: {
    /** 关键字 */
    keyword: string;
    /** 页码 */
    pageNum: number;
    /** 每页大小 */
    pageSize: number;
  };
  Response: ApiResponse<FeedbackPersonResult[]>;
}
