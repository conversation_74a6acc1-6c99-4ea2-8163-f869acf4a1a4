import type { ApiResponse } from '../common';

export type FeedbackChannelsType = {
  /** code */
  channelCode: string;
  /** 名称 */
  channelName: string;
}

export type AgentsItem = {
  /** code */
  agentCode: string;
  /** 名称 */
  agentName: string;
}

export type FeedbackTagsItem = {
  /** code */
  tagCode: string;
  /** 名称 */
  tagName: string;
}

/** 数据反馈列表Item */
export interface DataFeedbackListItem {
  /** 反馈人 */
  feedbackPerson: string;
  /** 所属分支部门 */
  department: string;
  /** 反馈时间 */
  feedbackTime: string;
  /** 反馈渠道 */
  feedbackChannels: FeedbackChannelsType;
  /** 场景覆盖 */
  sceneCoverage: string;
  /** 涉及Agent */
  agents: AgentsItem[];
  /** 反馈标签 */
  feedbackTags: FeedbackTagsItem[];
  /** 详细原因 */
  reasons: string;
  /** traceID */
  traceId: string;
  /** 会话Id */
  conversationId: string;
}

/** 数据反馈列表分页数据 */
export interface DataFeedbackListPage {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总条数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
}

/** 数据反馈列表 */
export interface DataFeedbackList {
  /** 数据反馈列表数据 */
  list: DataFeedbackListItem[];
  /** 分页数据 */
  page: DataFeedbackListPage;
}

/** 查询数据反馈列表-接口TS */
export interface IQueryDataFeedbackList {
  Query: {
    /** 数据类型 */
    dataType: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
    /** 反馈人 */
    empId: string;
    /** 所属部门 */
    orgIdList: string[];
    /** 反馈标签 */
    feedbackTags?: string[];
    /** 反馈渠道 */
    feedbackChannels: string[];
    /** 场景覆盖 */
    sceneCoverages: string[];
    /** 涉及Agent */
    agents: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 排序字段 */
    sortField: string;
    /** 排序顺序 */
    sortOrder?: string | null;
  };
  Response: ApiResponse<DataFeedbackList>;
}
