import type { ApiResponse } from '../common';

/** 会话详情 */
export interface ConversationDetailsResult {
  list: any[];
  nextOffset: number;
  noMore: boolean;
}

/** 查询会话详情-接口TS */
export interface IQueryConversationDetails {
  Query: {
    /** 提问人工号 */
    empId: string;
    /** appId */
    appId: string;
    /** 会话反馈类型 */
    feedbackResult: string;
    /** 会话Id */
    conversationId: string;
    /** 页码 */
    pageNum: number;
    /** 每页大小 */
    pageSize: number;
  };
  Response: ApiResponse<ConversationDetailsResult>;
}

/** 提交运营分析-接口TS */
export interface ISubmitOperationAnalysis {
  Query: {
    /** 反馈id */
    feedbackId: string;
    /** 消息id */
    messageId: string;
    /** 分析结果 */
    analysisResult: string;
  };
  Response: ApiResponse<ConversationDetailsResult>;
}
