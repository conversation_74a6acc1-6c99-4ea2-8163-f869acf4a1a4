import type { ApiResponse } from '../common';

/** 会话信息 */
export interface ConversationInfoResult {
  /** 会话ID */
  conversationId: string;
  /** 提问人 */
  questioner: string;
  /** 员工类型 */
  tgType: string;
  /** 部门 */
  department: string;
  /** 首次提问时间 */
  questionTime: string;
  /** 末次提问时间 */
  updateTime: string;
  /** 问题数 */
  questionNum: number;
   /** 子问题数 */
  subQuestionNum: number;
  /** 点赞数 */
  likesNum: number;
  /** 点踩数 */
  dislikesNum: number;
}

/** 查询会话信息-接口TS */
export interface IQueryConversationInfo {
  Query: {
    /** 会话Id */
    conversationId: string;
  };
  Response: ApiResponse<ConversationInfoResult>;
}
