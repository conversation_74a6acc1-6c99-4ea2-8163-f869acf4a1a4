import type { ApiResponse } from '../common';

export type DictItemType = {
  /** 名称 */
  label: string;
  /** code */
  value: string;
}

/** 反馈标签和Agent字典 */
export interface DictResult {
  answerSourceDict: DictItemType[];
  /** 反馈标签字典数据 */
  tagDict: DictItemType[];
  /** Agent字典数据 */
  agentDict: DictItemType[];
  /** 员工类型字典数据 */
  tgTypeDict: DictItemType[];
}

/** 查询反馈标签和Agent字典-接口TS */
export interface IQueryFeedbackTagsAndAgentDict {
  Response: ApiResponse<DictResult[]>;
}

