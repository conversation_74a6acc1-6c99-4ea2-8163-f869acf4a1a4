/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-len */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录相关services
 */
import type { IQueryConversationList } from './types/api/conversationRecord/queryConversationList';
import type { IQueryConversationStatics } from './types/api/conversationRecord/queryConversationStatics';
import type { IQueryQuestionList, IQuerySubQuestionList } from './types/api/conversationRecord/queryQuestionList';
import type { IQueryQuestionStatics } from './types/api/conversationRecord/queryQuestionStatics';
import type { IDownloadQuestionList } from './types/api/conversationRecord/downloadQuestionList';

import request from '@/utils/request';

/** 查询会话维度列表 */
export async function queryConversationList(data: IQueryConversationList['Query']): Promise<IQueryConversationList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/queryConversationList', { data });
}

/** 查询会话维度的统计信息 */
export async function queryConversationStatics(data: IQueryConversationStatics['Query']): Promise<IQueryConversationStatics['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/queryConversationStatics', { data });
}

/** 查询问题维度列表 */
export async function queryQuestionList(data: IQueryQuestionList['Query']): Promise<IQueryQuestionList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/queryQuestionList', { data });
}

/** 查询问题维度的统计信息 */
export async function queryQuestionStatics(data: IQueryQuestionStatics['Query']): Promise<IQueryQuestionStatics['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/queryQuestionStatics', { data });
}

/** 下载问题维度列表 */
export async function downloadQuestionList(data: IDownloadQuestionList['Query']): Promise<IDownloadQuestionList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/file/exportQuestionExcel', { data });
}
/** 查询子问题维度列表 */
export async function querySubQuestionList(data: IQuerySubQuestionList['Query']): Promise<IQuerySubQuestionList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/querySubQuestionList', { data });
}
/** 下载问题维度列表 */
export async function downloadSubQuestionList(data: IDownloadQuestionList['Query']): Promise<IDownloadQuestionList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/file/exportSubQuestionExcel', { data });
}
/** 获取答案来源字典 */
export async function queryDict(data: IDownloadQuestionList['Query']): Promise<IDownloadQuestionList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/queryDict', { data });
}
/** 查询问题维度的统计信息 */
export async function querySubQuestionStatics(data: IQueryQuestionStatics['Query']): Promise<IQueryQuestionStatics['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/conversation/getSubQuestionStatisticsInfo', { data });
}
