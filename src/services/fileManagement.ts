/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-len */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-10
 * @Description：知识库管理-文件管理相关services
 */
import type { ApiResponse } from './types/api/common';
import type { IGetFileInfoList } from './types/api/fileManagement/getFileInfoList';
import type { IGetFileAnalysisResult } from './types/api/fileManagement/getFileAnalysisResult';
import type { IGetAnalysisiMarkdownResult } from './types/api/fileManagement/getAnalysisMarkDown';
import type { ISaveAnalysisMarkDown } from './types/api/fileManagement/saveAnalysisMarkDown';
import type { IGetMarkDownChunks } from './types/api/fileManagement/getMarkDownChunks';
import type { IQueryDataSources } from './types/api/fileManagement/queryDataSources';
import type { IQueryFileCategory } from './types/api/fileManagement/queryFileCategory';
import type { IQueryFileManagementList } from './types/api/fileManagement/queryFileManagementList';
import type { IUpdateFileCategory } from './types/api/fileManagement/updateFileCategory';
import type { IUpdateFileStatus } from './types/api/fileManagement/updateFileStatus';
import type { IGetPreviewUrl } from './types/api/fileManagement/getPreviewUrl';
import type { IRecallTest } from './types/api/fileManagement/recallTest';
import type { IDeleteChunk } from './types/api/fileManagement/deleteChunk';
import type { ISaveChunk } from './types/api/fileManagement/saveChunk';
import type { ICheckoutChunk } from './types/api/fileManagement/checkoutChunk';
import type { IGetFileChunkProcess } from './types/api/fileManagement/getFileChunkProcess';

import request from '@/utils/request';

/** 获取文档解析等页面需要的文档信息 */
export async function getFileInfoList(data: IGetFileInfoList['Query']): Promise<IGetFileInfoList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileAnalysis/getFileInfoList', { data });
}

/** 获取文件的解析结果 */
export async function getFileAnalysisResult(data: IGetFileAnalysisResult['Query']): Promise<IGetFileAnalysisResult['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileAnalysis/getFileAnalysisResult', { data });
}

/** 获取文件的分片结果 */
export async function getFileChunkProcess(data: IGetFileChunkProcess['Query']): Promise<IGetFileChunkProcess['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileAnalysis/getFileChunkProcess', { data });
}

/** 获取文件的解析结果 */
export async function getPreviewUrl(data: IGetPreviewUrl['Query']): Promise<IGetPreviewUrl['Response']> {
  return request.post('/fspa/aorta/ai/api/cloud/preview', { data });
}

/** 获取文件解析后的MarkDown文件内容 */
export async function getAnalysisMarkDown(data: IGetAnalysisiMarkdownResult['Query']): Promise<IGetAnalysisiMarkdownResult['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileAnalysis/getAnalysisMarkDown', { data });
}

/** 保存MarkDown文件内容 */
export async function saveAnalysisMarkDown(data: ISaveAnalysisMarkDown['Query']): Promise<ISaveAnalysisMarkDown['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileAnalysis/saveAnalysisMarkDown', { data });
}

/** 获取文档切片列表 */
export async function getMarkDownChunks(data: IGetMarkDownChunks['Query']): Promise<IGetMarkDownChunks['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/chunk/getMarkDownChunks', { data });
}

/** 删除文档切片 */
export async function deleteChunk(data: IDeleteChunk['Query']): Promise<IDeleteChunk['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/chunk/deleteChunk', { data });
}

/** 保存文档切片 */
export async function saveChunk(data: ISaveChunk['Query']): Promise<ISaveChunk['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/chunk/saveChunk', { data });
}

/** 一键校验切片 */
export async function checkoutChunk(data: ICheckoutChunk['Query']): Promise<ICheckoutChunk['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/chunk/checkoutChunk', { data });
}

/** 召回测试 */
export async function recallTest(data: IRecallTest['Query']): Promise<IRecallTest['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/Vector/recallTest', { data });
}

/** 查询数据来源 */
export async function queryDataSources(): Promise<IQueryDataSources['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/dataSource/queryDataSources');
}

/** 查询文件类目 */
export async function queryFileCategory(data: IQueryFileCategory['Query']): Promise<IQueryFileCategory['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/ai/manager/pc/category/queryFileCategory', { data });
}

/** 查询文件管理字典 */
export async function queryDict(): Promise<IQueryDataSources['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileManagement/queryDict');
}

/** 查询文件管理列表 */
export async function queryFileManagementList(data: IQueryFileManagementList['Query']): Promise<IQueryFileManagementList['Response']> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileManagement/fileManagementList', { data });
}

/** 单个或批量编辑类目 */
export async function updateFileCategory(data: IUpdateFileCategory['Query']): Promise<ApiResponse<boolean>> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileManagement/updateFileCategory', { data });
}

/** 编辑是否启用 */
export async function updateFileStatus(data: IUpdateFileStatus['Query']): Promise<ApiResponse<boolean>> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileManagement/updateFileStatus', { data });
}

/** 删除文件 */
export async function deleteFile(data: { fileId: string }): Promise<ApiResponse<boolean>> {
  return request.post('/fspa/aorta/ai/api/desktop/knowledgebase/management/pc/FileManagement/deleteFile', { data });
}

/** 文件上传 */
export async function uploadFile(query: { formData: FormData, onUploadProgress?: (e: ProgressEvent) => void}): Promise<any> {
  const { formData, onUploadProgress } = query;
  return request.post('/fspa/aorta/ai/api/desktop/s3file/uploadFile', {
    data: formData,
    onReqProgress: onUploadProgress,
  });
}



