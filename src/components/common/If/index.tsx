/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-15
 * @Description  : 三元表达式判断组件
 */

import React, { Fragment } from 'react';
import _ from 'lodash';

import type { IProps } from './data.d';

const If: React.FC<IProps> = (props) => {
  const { when } = props;

  if (!when) {
    return null;
  }

  return (
    <Fragment>
      {React.Children.map(props.children, child => child)}
    </Fragment>
  );
};

export default If;
