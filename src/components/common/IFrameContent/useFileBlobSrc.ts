/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-16
 * @Description  : 根据文件流方式获取源文档的Blob对象后生成src地址
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';

import { useEffect, useState } from 'react';
import _ from 'lodash';

import { FileStreamTypes } from '@/components/FileAnalysis/config';

interface ReturnData {
  fileBlobSrc: string,
  loading: boolean,
}

/**
 * 获取文档流的src地址
 * @param fileUrl 文件流接口地址
 * @param fileType 文件流类型
 * @returns 文档流src地址
 */
function useFileBlobSrc(fileUrl: string, fileType: IFileInfo['fileType']): ReturnData {
  const [fileBlobSrc, setFileBlobSrc] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let url = '';
    setLoading(true);
    // 从接口获取文件流
    fetch(fileUrl, { method: 'GET' })
      .then(response => response.blob())
      .then(blob => {
        // 重新构造Blob并覆盖类型
        const type = FileStreamTypes[fileType] || 'application/octet-stream';
        const correctedBlob = new Blob([blob], { type });
        // 生成临时URL并赋值给iframe
        url = URL.createObjectURL(correctedBlob);
        setFileBlobSrc(url);
      })
      .catch(error => console.error('===swb===useFileBlobSrc::Error:', error))
      .finally(() => setLoading(false));

    return () => {
      if (!_.isEmpty(url)) {
        URL.revokeObjectURL(url);
      }
    };
  }, [fileUrl, fileType]);

  return {
    fileBlobSrc,
    loading,
  };
}

export default useFileBlobSrc;
