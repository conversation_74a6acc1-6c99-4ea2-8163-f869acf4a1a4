/*
 * <AUTHOR> sunweibin<********>
 * @Date         : 2025-04-15
 * @Description  : 渲染iframe页面的组件
 */
import type{ IProps } from './data.d';

import React, { useCallback } from 'react';
import _ from 'lodash';
import { Spin } from '@ht/sprite-ui';

import useFileBlobSrc from './useFileBlobSrc';

import styles from './styles.less';

const IFrameContent = (props: IProps) => {
  const { title, fileType, fileUrl } = props;
  const { fileBlobSrc, loading } = useFileBlobSrc(fileUrl, fileType);

  const handleLoad = useCallback(() => {
    if (!_.isEmpty(fileBlobSrc)) {
      URL.revokeObjectURL(fileBlobSrc);
    }
  }, [fileBlobSrc]);

  if (loading) {
    return (
      <div className={styles.iframeLoading}>
        <Spin tip="正在加载文件..." />
      </div>
    );
  }

  return (
    <iframe
      title={title}
      src={fileBlobSrc}
      onLoad={handleLoad}
      className={styles.iframe}
    />
  );
};


export default IFrameContent;
