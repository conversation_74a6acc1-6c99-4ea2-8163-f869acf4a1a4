import React, { useState, useRef, useEffect, useCallback } from 'react';
import styles from './index.less';

interface EllipsisProps {
  /** 文字内容 */
  content: string;
  /** 超过几行省略，默认为 1 */
  rows?: number;
  /** 是否能展开，默认为 false */
  expandable?: boolean;
  /** 展开的文案，默认为 "展开" */
  expandText?: string;
  /** 折叠的文案，默认为 "收起" */
  collapseText?: string;
  /** 展开按钮的样式类名 */
  expandTextClassName?: string;
  /** 文字的点击事件 */
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  /** 容器的样式类名 */
  className?: string;
  /** 容器的内联样式 */
  style?: React.CSSProperties;
}

const Ellipsis: React.FC<EllipsisProps> = ({
  content,
  rows = 1,
  expandable = false,
  expandText = '展开',
  collapseText = '收起',
  expandTextClassName = '',
  onClick,
  className = '',
  style = {},
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 检测文本是否溢出
  const checkOverflow = useCallback(() => {
    if (!contentRef.current) { return; }

    const element = contentRef.current;
    // 比较滚动高度和客户端高度
    const isOverflow = element.scrollHeight > element.clientHeight;
    setIsOverflowing(isOverflow);
  }, []);

  // 初始化和窗口变化时检测溢出
  useEffect(() => {
    checkOverflow();

    // 添加窗口变化监听
    const handleResize = () => {
      checkOverflow();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [checkOverflow, content, rows]);

  // 内容变化时重新检测
  useEffect(() => {
    // 使用 setTimeout 确保 DOM 更新完成后再检测
    const timer = setTimeout(() => {
      checkOverflow();
    }, 0);

    return () => clearTimeout(timer);
  }, [content, rows, checkOverflow]);

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(prev => !prev);
  };

  const handleContentClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onClick) {
      onClick(e);
    }
  };

  // 计算行高和最大高度
  const lineHeight = 1.5; // 假设行高为 1.5em
  const maxHeight = isExpanded ? 'none' : `${lineHeight * rows}em`;

  const showExpandButton = expandable && isOverflowing;

  return (
    <div
      className={styles.ellipsisContainer}
      style={style}
      onClick={handleContentClick}
    >
      <div
        ref={contentRef}
        className={isExpanded ? styles.ellipsisContentExpanded : styles.ellipsisContent}
        style={{
          WebkitLineClamp: isExpanded ? 'unset' : rows,
          maxHeight,
        }}
      >
        {content}
      </div>

      {showExpandButton && (
        <button
          type="button"
          className={expandTextClassName ?? styles.ellipsisExpandBtn}
          onClick={handleToggleExpand}
        >
          {isExpanded ? collapseText : expandText}
        </button>
      )}
    </div>
  );
};

export default Ellipsis;
