import React from 'react';
import _ from 'lodash';

import styles from '../index.less';

function ImageCard(props: any) {
  const {
    data,
    onClick,
  } = props;

  const handleDetailJump = () => {
    onClick(data?.content);
  };

  return (
    <div className={`${styles.card} ${styles.imageCard}`}>
      <div className={styles.top}>
        <img src={data?.content?.imgUrl} />
      </div>
      <div className={styles.bottom} onClick={handleDetailJump}>
        <div className={styles.text}>{data?.content?.summary}</div>
        {/* <div className={styles.arrow}>{'>'}</div> */}
      </div>
    </div>
  );
}

export default ImageCard;
