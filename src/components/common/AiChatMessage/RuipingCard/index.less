.card {
  margin-top: 20px;
}

.imageCard {
  position: relative;
  width: 100%;
  height: 231px;
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 8%);
  border-radius: 12px;

  .top {
    height: 178px;
    overflow: hidden;
    border-radius: 12px 12px 0 0;

    img {
      width: 100%;
      max-height: auto;
      display: block;
      border-radius: 12px 12px 0 0;
    }
  }

  .bottom {
    height: 53px;
    padding: 0 12px;
    display: flex;
    align-items: center;

    .text {
      flex: 0 1 310px;
      height: 19px;
      font-size: 14px;
      color: #333;
      line-height: 19px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .arrow {
      flex: 1;
    }
  }
}

.mediaCard {
  width: 100%;
  height: auto;
  background: #edf4ff;
  border-radius: 8px;
  padding: 12px;

  .title {
    height: 20px;
    font-size: 14px;
    color: #3e74f7;
    line-height: 20px;
    background-image: url("./images/icon_title.png");
    background-repeat: no-repeat;
    background-position: 0 center;
    background-size: 12px 12px;
    text-indent: 16px;
  }

  .summary {
    width: 100%;
    max-height: 120px;
    font-size: 14px;
    font-weight: 400;
    color: #4a4a4a;
    line-height: 24px;
    overflow: hidden;
  }

  .file {
    width: 100%;
    height: 100px;
    margin-top: 12px;
    background: rgba(255, 255, 255, 80%);
    border-radius: 8px;
    padding: 17px 12px;
    display: flex;
    justify-content: space-between;

    .fileIcon {
      flex: 0 1 16px;

      .icon {
        width: 16px;
        height: 20px;
      }
    }

    .fileName {
      flex: 0 1 232px;
      height: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .download {
      flex: 0 1 18px;
      height: 18px;

      svg {
        path {
          fill: #3e74f7;
        }
      }

      .icon {
        width: 18px;
        height: 18px;
      }
    }
  }
}

.mediaCardPdf {
  width: 100%;
  height: auto;
  background: #edf4ff;
  border-radius: 8px;
  padding: 12px;

  .title {
    height: 20px;
    font-size: 14px;
    color: #3e74f7;
    line-height: 20px;
    background-image: url("./images/icon_title.png");
    background-repeat: no-repeat;
    background-position: 0 center;
    background-size: 12px 12px;
    text-indent: 16px;
  }

  .summary {
    width: 100%;
    max-height: 120px;
    font-size: 14px;
    font-weight: 400;
    color: #4a4a4a;
    line-height: 24px;

    :global {
      .mpa-mobapp-host-ellipsis {
        line-height: 1 !important;
      }
    }
  }

  .file {
    width: 100%;
    height: 82px;
    margin-top: 12px;
    background: rgba(255, 255, 255, 80%);
    border-radius: 8px;
    padding: 17px 12px;
    display: flex;
    justify-content: space-between;

    .fileName {
      width: 233px;
      height: 48px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      line-height: 24px;
      overflow: hidden;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      white-space: normal;
    }

    .fileIcon {
      width: 48px;
      height: 52px;
      position: relative;
      background-image: url("./images/icon_pdf.png");
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 48px 52px;

      .download {
        width: 54px;
        height: 22px;
        background: rgba(0, 0, 0, 40%);
        border-radius: 11px;
        display: flex;
        position: absolute;
        top: 30px;
        left: -3px;
        align-items: center;
        justify-content: space-around;
        font-size: 13px;
        font-weight: 400;
        color: #fff;

        .icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.expandText {
  color: #4a4a4a !important;
}
