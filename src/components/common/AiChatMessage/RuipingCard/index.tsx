import React from 'react';
import _ from 'lodash';

import ImageCard from './ImageCard';
import MediaCard from './MediaCard';
import PdfCard from './PdfCard';
import styles from './index.less';


function Card(props: any) {
  const {
    cards,
  } = props;

  // 内容-投研内参-财富  10：/feedCenter/wealthResearchDetails
  // 内容-投研内参-行业  11：/feedCenter/researchReportDetail
  // 内容-投研内参-公司  12：/feedCenter/researchReportDetail
  // 内容-投研内参-宏观  13：/feedCenter/researchReportDetail
  // 内容-投研内参-债券  14：/feedCenter/researchReportDetail
  // 内容-投研内参-金工  15：/feedCenter/researchReportDetail
  // 内容-投研内参-策略  18：/feedCenter/researchReportDetail
  // 内容-投研内参-省心内参    21：/feedCenter/shengXinNeiCanReportDetail
  // 内容-资讯推荐-省心研究院  22：/feedCenter/researchInfoDetail
  // 内容-资讯推荐-投资科普    23：/feedCenter/researchInfoDetail
  const getJumpData = (cardData: any) => {
    const category = cardData?.category;
    let url = '';
    let payload: any = {};
    switch (category) {
      case '10':
        url = '/feedCenter/wealthResearchDetails';
        payload = {
          reportCode: cardData.docId,
        };
        break;
      case '11':
      case '12':
      case '13':
      case '14':
      case '15':
      case '18':
        url = '/feedCenter/researchReportDetail';
        payload = {
          id: cardData.docId,
        };
        break;
      case '21':
        url = '/feedCenter/shengXinNeiCanReportDetail';
        payload = {
          id: cardData.docId,
        };
        break;
      case '22':
        url = '/feedCenter/researchInfoDetail';
        payload = {
          fspColumnName: '省心研究院',
          infoId: cardData?.docId,
          type: 'zlzc',
          zxType: 'zlzc',
        };
        break;
      case '23':
        url = '/feedCenter/researchInfoDetail';
        payload = {
          fspColumnName: '投资科普',
          infoId: cardData?.docId,
          type: 'zlzc',
          zxType: 'zlzc',
        };
        break;
      default:
        break;
    }
    return {
      url,
      payload,
    };
  };


  const renderCard = (card: any, index: number) => {
    const type = (card?.type || '').toLocaleLowerCase();
    if (type === 'image-card') {
      return (<ImageCard data={card} key={index} />);
    }
    if (type === 'media-card') {
      const mediaType = (card?.content?.mediaType || '').toLocaleLowerCase();
      if (mediaType === 'pdf') {
        return <PdfCard data={card} key={index} />;
      }
      return <MediaCard data={card} key={index} />;
    }
    return null;
  };

  return (
    <div className={styles.card}>
      {_.map(cards, (card: any, index: number) => {
        return renderCard(card, index);
      })
      }
    </div>
  );
}

export default Card;
