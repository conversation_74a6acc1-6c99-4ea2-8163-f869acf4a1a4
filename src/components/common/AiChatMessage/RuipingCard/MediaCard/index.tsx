import React from 'react';
import _ from 'lodash';
import Ellipsis from '../Ellipsis';
import FileIcon from '../images/icon_file.svg';

import styles from '../index.less';

function MediaCard(props: any) {
  const {
    data,
  } = props;


  return (
    <div className={`${styles.card} ${styles.mediaCard}`}>
      <div className={styles.title}>
        华泰甄选
      </div>
      <div className={styles.summary}>
        <Ellipsis
          content={data?.content?.summary}
          rows={5}
          expandText=">"
          expandable={false}
        />
      </div>
      <div className={styles.file}>
        <div className={styles.fileIcon}>
          <FileIcon className={styles.icon} />
        </div>
        <div className={styles.fileName}>{data?.content?.fileTitle}</div>
        {/* <div className={styles.download} onClick={handleDownload}>
          <DownloadBlueIcon className={styles.icon} />
        </div> */}
      </div>
    </div>
  );
}

export default MediaCard;
