import React from 'react';
import Ellipsis from '../Ellipsis';
import _ from 'lodash';

import styles from '../index.less';

function PdfCard(props: any) {
  const {
    data,
  } = props;


  return (
    <div className={`${styles.card} ${styles.mediaCardPdf}`}>
      <div className={styles.title}>
        华泰甄选
      </div>
      <div className={styles.summary}>
        <Ellipsis
          content={data?.content?.summary}
          rows={2}
          expandText=">"
          expandable={false}
          expandTextClassName={styles.expandText}
        />
      </div>
      <div className={styles.file}>
        <div className={styles.fileName}>{data?.content?.fileTitle}</div>
        {/* <div className={styles.fileIcon}>
          <div className={styles.download} onClick={handleDownload}>
            <DownloadIcon className={styles.icon} />
            下载
          </div>
        </div> */}
      </div>
    </div>
  );
}

export default PdfCard;
