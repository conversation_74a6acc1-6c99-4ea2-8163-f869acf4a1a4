/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable complexity */
/* eslint-disable max-len */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-28
 * @Description：AI问答消息组件
 */
import React, { useCallback, useEffect, useState } from 'react';
import _ from 'lodash';
// 引入组件
import { Message } from '@aorta/chatui';
// 引入组件样式
import '@aorta/chatui/dist/index.css';
import { createUuid } from './util';
import Card from './RuipingCard';
import MiaoQuanLabelGroupDetail from './MiaoquanCard/MiaoQuanLabelGroupDetail';
import MiaoQuanCustCount from './MiaoquanCard/MiaoQuanCustCount';
import MiaoQuanCreateButton from './MiaoquanCard/MiaoQuanCreateButton_';

type AiChatMessageProps = {
  /** 数据源 */
  dataList: any;
  /** 提交运营分析 */
  onSubmitAnalysis: (msg: any) => void;
}

export interface ReferenceProps {
  /**
   * 链接地址
   */
  url?: string;
  /**
   * 网页名称
   */
  title: string;
  source?: string;
  docName?: string;
  downloadUrl?: string;
  fileType?: string;
  citationIndex?: number;
}

export interface ContentsProps {
  references?: ReferenceProps[];
}
const AiChatMessage: React.FC<AiChatMessageProps> = ({
  dataList,
  onSubmitAnalysis,
}) => {
  const [messageList, setMessageList] = useState<any[]>([]);
  const aggregateData = (data: any[]) => {
    // 用于存储聚合结果的对象
    const aggregatedData = {} as any;

    // 遍历数据数组
    data.forEach((item: any) => {
      const { messageId } = item;

      // 如果该类别还未出现过，则添加到聚合对象中
      if (!aggregatedData?.[messageId]) {
        aggregatedData[messageId] = [];
      }

      // 将当前项添加到对应类别的数组中
      aggregatedData[messageId].push(item);
    });

    return aggregatedData;
  };

  const replaceCitations = (str: string, msg: any, references: ReferenceProps[]) => {
    // 正则表达式匹配[citation:数字]模式
    const citationPattern = /\[citation:(\d+)\]/g;
    // 替换函数，用于生成HTML结构
    const replacementFunction = (_match: string, citationNumber: string) => {
      // 构造id属性值messageId+agent+problem_id+citationNumber
      const id = `${msg?.messageId}+${msg?.agent}+${msg?.problem_id === null || msg?.problem_id === undefined ? msg?.problemId : msg?.problem_id}+${citationNumber}`;
      // 找到相应的引用详情确定是外部引用还是内部引用
      const reference = references?.find((item: ReferenceProps) => Number(item?.citationIndex) === Number(citationNumber));
      // 返回HTML结构字符串
      return `<span data-id="${id}" class="ReferenceCitation ${reference?.source === 'inner' ? 'InnerCitation' : 'OuterCitation'}">${citationNumber}</span>`;
    };

    // 使用replace方法和正则表达式进行全局替换
    return str?.replace(citationPattern, replacementFunction);
  };

  const replaceToolStartAndEnd = (str: string) => {
    // 正则表达式匹配[tool_start]模式
    const toolStartPattern = /\[tool_start(.*?)\]/g;
    // 正则表达式匹配[tool_end]模式
    const toolEndPattern = /\[tool_end(.*?)\]/g;
    const replaceToolStart = (_match: string, text: string) => {
      console.log('toolStart', text);
      // 返回HTML结构字符串
      return `<div class="Toolstart">${text}<span class="TypingEllipsis"><span class="TypingDot"></span><span class="TypingDot"></span><span class="TypingDot"></span></span></div>`;
    };

    const replaceToolEnd = (_match: string, text: string) => {
      console.log('toolEnd', text);
      // 返回HTML结构字符串
      return `<div class="Toolend">${text}</div>`;
    };

    if (toolEndPattern.test(str)) {
      const regex = /<div class="Toolstart">(.*?)<span class="TypingEllipsis">(\s*<span class="TypingDot"><\/span>\s*){3}<\/span><\/div>/g;
      if (regex.test(str)) {
        return  str.replace(regex, '').replace(toolEndPattern, replaceToolEnd);
      } else if (toolStartPattern.test(str)) {
        return  str.replace(toolStartPattern, '').replace(toolEndPattern, replaceToolEnd);
      } else {
        return str.replace(toolEndPattern, replaceToolEnd);
      }
    } else if (toolStartPattern.test(str)) {
      return str.replace(toolStartPattern, replaceToolStart);
    } else {
      return str;
    }
  };

  const extractAndDeduplicateReferences = (contents: ContentsProps[]): Array<ReferenceProps> => {
    // 初始化一个 Map 用于存储去重后的 references
    // Map 的键是由 title 和 docName 组合而成的唯一标识符
    // Map 的值是去重后的 reference 对象
    const uniqueReferencesMap = new Map<string, ReferenceProps>();

    // 遍历 contents 数组
    contents?.forEach(item => {
      // 假设每个 item 都有一个名为 references 的数组属性
      if (Array.isArray(item?.references)) {
        // 使用 forEach 遍历每个 item 的 references 数组
        item?.references?.forEach(reference => {
          // 构造唯一标识符：如果 title 和 docName 都存在，则使用它们的组合；否则只使用存在的那个字段作为标识符
          const uniqueIdentifier = reference?.title || reference?.docName || '';

          // 检查 Map 中是否已经存在该唯一标识符
          if (!uniqueReferencesMap.has(uniqueIdentifier)) {
            // 如果不存在，则将该 reference 对象添加到 Map 中
            uniqueReferencesMap.set(uniqueIdentifier, reference);
          }
        });
      }
    });

    // 将 Map 中的值转换为一个数组并返回（这就是去重后的 references 数组）
    return Array.from(uniqueReferencesMap.values());
  };

  const dealwithHistoryMessageList = useCallback((data) => {
    const list = aggregateData(data ?? []);
    const msgs: any = [];

    // 对每个按照messageId分组后的数据进行处理
    Object.keys(list).forEach((messageId) => {

      const msgList = list?.[messageId] ?? [];
      const questionMsg = msgList?.find((item: any) => item?.role === 'user') ?? {};
      // 追加问题
      msgs.push({
        _id: `${messageId}-question`,
        type: 'text',
        messageId,
        position: 'right',
        createdAt: questionMsg?.createTime,
        needFeedback: false,
        showToken: false,
        showHallucination: false,
        content: {
          text: questionMsg?.list?.[0]?.content?.text || '',
        },
      });

      // 开始处理答案的逻辑
      const firstAnswerMsg = msgList?.find((item: any) => item?.role !== 'user') ?? {};

      const commonParms = {
        _id: messageId,
        messageId,
        position: 'left',
        createdAt: firstAnswerMsg?.createTime,
        needFeedback: firstAnswerMsg?.needFeedback ?? false,
        feedbackResult: firstAnswerMsg?.feedbackResult ?? null,
        showToken: false,
        showHallucination: false,
        type: 'markdown',
        feedback: firstAnswerMsg?.feedback ?? null,
        traceId: firstAnswerMsg?.langfuseTraceId ?? null,
        endFlag: true,
      };
      const coordinatorText = msgList?.find((item: any) => item?.agent === 'coordinator')?.list?.[0]?.content?.text ?? '';
      // 这种情况直接追加coordinator的语句以后不许要再往下走了
      if (coordinatorText) {
        msgs.push({
          ...commonParms,
          content: {
            text: coordinatorText,
          },
        });
        return;
      }
      const problemAnalyzeText = msgList?.find((item: any) => item?.agent === 'problem_analyze')?.list?.[0]?.content?.text ?? '';
      const reporterText = msgList?.find((item: any) => item?.agent === 'reporter')?.list?.[0]?.content?.text ?? '';
      const problemDecomposeJson = msgList?.find((item: any) => item?.agent === 'problem_decompose') ?? {};
      const originProblemDecompose = problemDecomposeJson?.list?.[0]?.content?.disassembly_problems ?? [];
      const yewenProblemsDecompose = originProblemDecompose?.filter((item: any) => item?.candidate_agent === 'yewen');
      const deepseekProblemsDecompose = originProblemDecompose?.filter((item: any) => item?.candidate_agent === 'hiagent_search' || item?.candidate_agent === 'default_local_llm');
      const ruipingProblemsDecompose = originProblemDecompose?.filter((item: any) => item?.candidate_agent === 'ruiping');
      const miaoquanProblemsDecompose = originProblemDecompose?.filter((item: any) => item?.candidate_agent === 'cust_agent');
      const problemDecompose = [...yewenProblemsDecompose, ...ruipingProblemsDecompose, ...deepseekProblemsDecompose, ...miaoquanProblemsDecompose];
      let yewenThink = '';
      let deepseekThink = '';
      let ruipingThink = '';
      let miaoquanThink = '';

      // 用数组来装正文部分，可能包含text或card
      const formatTextArr = [];
      // 针对问题拆解的帧分组出来相应的思考和正文
      for (let i = 0; i < problemDecompose?.length; i++) {
        const relatedMsg = msgList?.find((item: any) => item?.agent === problemDecompose?.[i]?.candidate_agent
          && String(item?.problemId) === String(problemDecompose?.[i]?.problem_id));
        // 拼接参考资料，因为不确定排序完以后参考资料会不会在for循环的时候先设置到problemDecompose[i]里面，所以直接在外层设置好，正文引用颜色判断需要使用
        const msgReferences = relatedMsg?.list?.find((item: any) => item?.type === 'references')?.references ?? [];
        problemDecompose[i].references = msgReferences;
        // 历史接口默认设为true表示子问题已回答完毕
        problemDecompose[i].isProblemEnd = true;

        for (let j = 0; j < relatedMsg?.list?.length; j++) {
          const msg = relatedMsg?.list?.[j];
          // 按照agent拼接思考内容
          if (msg?.type === 'thinking') {
            if (relatedMsg?.agent === 'yewen') {
              yewenThink += `${msg?.content?.text ?? ''}\n`;
            }
            if (relatedMsg?.agent === 'hiagent_search' || relatedMsg?.agent === 'default_local_llm') {
              deepseekThink += `${msg?.content?.text ?? ''}\n`;
            }
            if (relatedMsg?.agent === 'ruiping') {
              ruipingThink += `${msg?.content?.text ?? ''}\n`;
            }
            if (relatedMsg?.agent === 'cust_agent') {
              miaoquanThink += `${msg?.content?.text ?? ''}\n`;
            }
          }
          // 拼接参考资料
          // if (msg?.type === 'references') {
          //   problemDecompose[i].references = msg?.references ?? [];
          // }
          // 拼接正文
          if (msg?.type === 'text') {
            const msgText = replaceCitations(msg?.content?.text ?? '', relatedMsg, problemDecompose?.[i]?.references);
            problemDecompose[i].content = msgText;
            formatTextArr.push({ type: 'text', content: msgText });
            problemDecompose[i].contentArr = formatTextArr;
          }
          // 出现睿评卡片的时候
          if (msg?.type === 'mas-card') {
            // problemDecompose[i].cards = [JSON.parse(msg?.content ?? '{}')]; // msg?.content里面包含content和type两个字段（type === 'image-card' || type === 'media-card'）
            problemDecompose[i].cards = [msg?.content ?? {}];
            formatTextArr.push({ type: 'card', content: [msg?.content ?? {}] });
            problemDecompose[i].contentArr = formatTextArr;
          }
        }
      }

      const thinkLinksContent = [];
      thinkLinksContent[0] = {
        title: '问题分析',
        thinkContent: problemAnalyzeText,
        isThinking: false,
      };

      if (yewenProblemsDecompose?.length > 0) {
        thinkLinksContent[1] = {
          title: '业务知识检索',
          thinkContent: yewenThink,
          isThinking: false,
        };
      }

      if (ruipingProblemsDecompose?.length > 0) {
        thinkLinksContent[2] = {
          title: '市场智研分析',
          // thinkContent: ruipingThink,
          thinkContent: replaceToolStartAndEnd(ruipingThink),
          isThinking: false,
        };
      }

      if (miaoquanProblemsDecompose?.length > 0) {
        thinkLinksContent[3] = {
          title: '智能客户洞察',
          thinkContent: miaoquanThink,
          isThinking: false,
        };
      }

      if (deepseekProblemsDecompose?.length > 0) {
        thinkLinksContent[4] = {
          title: 'DS大模型分析',
          thinkContent: deepseekThink,
          isThinking: false,
        };
      }

      const finalContent = [];
      for (let i = 0; i <= problemDecompose?.length; i++) {
        finalContent.push(problemDecompose?.[i]);
      }

      const msg = {
        ...commonParms,
        content: {
          text: reporterText ? `## **${reporterText}**` : '',
        },
        thinkLinks: thinkLinksContent,
        isThinking: false,
        contents: finalContent,
        uniqReferences: extractAndDeduplicateReferences(problemDecompose),
      };
      console.warn('msg', msg);
      msgs.push(msg);
    });
    setMessageList([...messageList, ...msgs]);
  }, []);

  useEffect(() => {
    if (dataList) {
      dealwithHistoryMessageList(dataList);
    }
  }, [dataList]);

  const encodeUrlParam = (url: string, paramName: string) => {
    const dummyBase = 'http://localhost/';
    const urlObj = new URL(url, dummyBase); // 支持相对 URL
    const params = urlObj.searchParams;

    if (params.has(paramName)) {
      const rawValue = params.get(paramName) ?? '';
      params.set(paramName, encodeURIComponent(rawValue)); // 仅编码该参数值
    }

    return `${urlObj.pathname}?${urlObj.searchParams.toString()}`;
  };
  const fetchFile = async (downloadUrl: string, fileType: string) => {
    // 只有html类型的内部文件会以downloadUrl的形式进行下载预览
    if (!downloadUrl) { return; };

    try {
      if (downloadUrl) {
        const updateUrl = encodeUrlParam(downloadUrl, 'filename');
        const response = await fetch(`/fspa${updateUrl}`);
        const blob = await response.blob();
        // 返回的blob的type为octet-stream，这里需要重新生成一个blob对象
        let fixedBlob: any;
        let mimeType: string;
        switch (fileType.toLocaleLowerCase()) {
          case 'pdf':
            mimeType = 'application/pdf';
            break;
          case 'html':
            mimeType = 'text/html;charset=utf-8';
            break;
          case 'txt':
          case 'csv':
          case 'json':
          case 'md':
          case 'log':
          case 'xml':
            mimeType = 'text/plain';
            break;
          default:
            mimeType = blob.type || 'application/octet-stream';
            break;
        }

        // eslint-disable-next-line prefer-const
        fixedBlob = new Blob([blob], { type: mimeType });
        if (fileType && fileType === 'html') { // html类型

          const htmlBlobUrl = URL.createObjectURL(fixedBlob);
          const win = window.open();
          if (!win) {
            // eslint-disable-next-line no-alert
            alert('请允许弹窗');
          } else {
            const htmlContent = document.createElement('div');
            htmlContent.innerHTML = `
                      <html>
                        <body>
                          <iframe src="${htmlBlobUrl}" style="width:100%;height:100%" sandbox="allow-same-origin allow-scripts"></iframe>
                        </body>
                      </html>
                    `;
            win.document.body.appendChild(htmlContent);
          }
        } else { // 不支持预览的类型
          // eslint-disable-next-line no-alert
          alert(`暂不支持该类型（${fileType}）的文件预览`);
          console.error(`暂不支持该类型（${fileType}）的文件预览`);
        }
      }
    } catch (err) {
      // eslint-disable-next-line no-alert
      alert('文件加载失败');
      console.error('文件加载失败', err);
    }
  };

  const downloadFileAndView = useCallback((url: string, type: string) => {
    fetchFile(url, type);
  }, []);

  const handleRenderCardContent = (cards: any) => {
    return (
      <Card cards={cards} />
    );
  };


  const addUUID = (item:any) => {
    return {
      ...item,
      uuid: createUuid(),
    };
  };


  const addCascadeUUID = (rules:any) => {
    return _.map(rules, (rule) => {
      const ruleItem = addUUID(rule);

      return {
        ...ruleItem,
        filtersData: _.map(rule?.filtersData || [], addUUID),
      };
    });
  };
  const getMiaoQuanRulesWithUUID = (data:any, aiInfo:any) => {
    const rules = data?.content?.rules || [];
    const newRules = addCascadeUUID(rules);

    return newRules;
  };

  const renderExternalCard = (data:any, aiInfo:any) => {
    const { messageId = 'temp' } = aiInfo;

    console.warn('===swb===renderExternalCard::', data);

    // 【AI-秒圈】的【圈客方案卡片】
    if (data?.type === 'MiaoQuan_LabelGroupDetail') {
      const newRules = getMiaoQuanRulesWithUUID(data, aiInfo);

      return (
        <MiaoQuanLabelGroupDetail
          key={messageId}
          data={newRules}
        />
      );
    }

    // 【AI-秒圈】的【人数预估卡片】
    if (data?.type === 'MiaoQuan_CustCount') {
      return (
        <MiaoQuanCustCount
          key={messageId}
          data={data?.content || {}}
        />
      );
    }

    // 【AI-秒圈】的【智能生成客群，按钮】
    if (data?.type === 'MiaoQuan_CreateLabelGroupButton') {

      return (
        <MiaoQuanCreateButton
          key={messageId}
          data={data?.content || {}}
          location={location}
        />
      );
    }

    return null;
  };

  const langfuseUrl = window.location.host === 'eip.htsc.com.cn' ? 'http://*************:3000/project/cmb9b22e70006qx070an0w0zc' : 'http://************:3000/project/cmcsoxnjp000eo508beut78ic';
  return (
    <div>
      {_.map(messageList, (msg: any) => (
        <Message
          {...msg}
          key={msg._id}
          isPreview={true}
          traceIdUrl={`${langfuseUrl}/traces?peek=${msg?.traceId}`}
          downloadFileAndView={downloadFileAndView}
          onSubmitAnalysis={onSubmitAnalysis}
          renderCardContent={handleRenderCardContent}
          renderExternalCard={renderExternalCard}
        />
      ))}
    </div>
  );
};

export default AiChatMessage;
