import _ from 'lodash';

/**
   * 整数部分千分位格式化的正则表达式
   * 例子：12345604 => 12,345,604
   */
const thousandInteger = /(\d{1,3})(?=(\d{3})+(?:$|\D))/g;
/**
   * 小数部分千分位格式化的正则表达式
   * 例子： 12345604 => 123,456,04
   */
const thousandDecimal = /(\d{3})(?=(\d{1,3})+)/g;

/**
 * 数字格式化
 * <AUTHOR>
 * @param {String|Number} no 需要进行千分位格式化的数字或者数字字符串
 * @param {Boolean} decimalNeedFormat=true 小数部分是否进行格式化
 * @param {String} thousandSeq=',' 千分位格式化符号
 * @param {Boolean} isRemoveZero=false 小数部分多余的0是否移除
 * @returns {String|null} 格式化后的字符串
 */
function thousandFormat(no = 0, decimalNeedFormat = true, thousandSeq = ',', isRemoveZero) {
  let numberString = String(no);
  if (isRemoveZero) {
    if (/\./.test(numberString)) {
      numberString = numberString.replace(/0*$/, '').replace(/\.$/, '');
    }
  }
  const replacement = `$1${thousandSeq}`;
  // 将数字差分成整数部分和小数部分
  const nArr = numberString.split('.');
  const itegerF = nArr[0].replace(thousandInteger, replacement);
  let decimalF = !_.isEmpty(nArr[1]) && nArr[1].replace(thousandDecimal, replacement);
  if (!decimalNeedFormat) {
    decimalF = !_.isEmpty(nArr[1]) && nArr[1];
  }
  if (!decimalF) {
    decimalF = numberString.indexOf('.') > -1 ? '.' : '';
  } else {
    decimalF = `.${decimalF}`;
  }
  return `${itegerF}${decimalF}`;
};

/**
   * 生成一个唯一的ID值
   * @param {*} len
   * @param {*} radix
   */
const createUuid = (len = 8, radix = 16) => {

  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  const uuid = [];

  if (len) {
    // Compact form
    // eslint-disable-next-line no-bitwise
    for (let i = 0; i < len; i++) { uuid[i] = chars[0 | Math.random() * radix]; }
  } else {
    // rfc4122, version 4 form
    let r;

    // rfc4122 requires these characters
    // eslint-disable-next-line no-multi-assign
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';

    // Fill in random data. At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (let i = 0; i < 36; i++) {
      if (!uuid[i]) {
        // eslint-disable-next-line no-bitwise
        const r = 0 | Math.random() * 16;
        // eslint-disable-next-line no-bitwise
        uuid[i] = chars[i === 19 ? r & 0x3 | 0x8 : r];
      }
    }
  }

  return uuid.join('');
};

export { thousandFormat, createUuid };
