/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-07-30
 * @Description  : AI-秒圈【智能生成客群】按钮
 * @Wiki         : http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=418055285
 */
import React, { useState } from 'react';
import type { History } from '@oula/oula';
import {
  RULES_STORAGE_KEY,
  NEW_GROUP_CREATION_PATH,
} from './config';

import styles from './index.less';

interface IProps {
  location?: any;
  data: any;
  hasClicked?: boolean;
  onClick?: () => void;
  history?: History;
}

const MiaoQuanCreateButton: React.FC<IProps> = (props) => {



  const { hasClicked, history } = props;

  const btnTips = {
    DEFAULT: '如有进一步需求，请继续在对话框中下达指令',
    CLICKED: '已为您智能生成，可在圈客功能中继续调整指标',
  };

  const [btnTip, setBtnTip] = useState(hasClicked ? btnTips.CLICKED : btnTips.DEFAULT);


  const getCurrentPathName = () => {
    const {
      location: {
        pathname,
      },
    } = props;

    return pathname;
  };


  const saveRulesDataToSessionStorage = (rules:any) => {
    window.sessionStorage.setItem(RULES_STORAGE_KEY, JSON.stringify(rules));
  };


  const handleCreateGroupBtnClick = () => {

    const currentPathName = getCurrentPathName();
    const { data } = props;

    // NOTE: SWB 2025-07-30 判断当前页面是否是【创建客群】页面
    // @see http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=418055285
    const ruleData = {
      rules: data?.rules || [],
      groupName: data?.groupName || '',
      groupDesc: data?.groupDesc || '',
    };

    if (currentPathName !== NEW_GROUP_CREATION_PATH) {
      // 如果【非-创建客群】页面，则直接使用push方式传递数据
      saveRulesDataToSessionStorage(ruleData);
      const pathInfo = `/customerCenter/newGroupCreation?groupType=myGroup&createType=13&from=MiaoQuan_CreateLabelGroupButton&storageKey=${RULES_STORAGE_KEY}`;
      history?.push(pathInfo);
      return;
    }

    if (currentPathName === NEW_GROUP_CREATION_PATH) {
      // 如果【是-创建客群】页面，则使用
      const miaoquanEvent = new CustomEvent('ai-miaoquan', { detail: ruleData });
      window.dispatchEvent(miaoquanEvent);
    }


    setBtnTip(btnTips.CLICKED);


    props.onClick?.();
  };

  return (
    <div className={styles.miaoquanCreateBtn}>
      <div className={styles.createBtn} onClick={handleCreateGroupBtnClick}>
        <div className={styles.btnText}>立 即 生 成 客 群</div>
        <div className={styles.arrowIcon} />
      </div>
      <div className={styles.description}>{btnTip}</div>
    </div>
  );
};


export default MiaoQuanCreateButton;
