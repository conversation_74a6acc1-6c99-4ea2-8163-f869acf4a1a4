.miaoquanCreateBtn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  max-width: 800px;
  min-width: 458px;
  height: 97px;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;

  .createBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    width: 235px;
    height: 48px;
    background: #fff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05);
    border-radius: 24px;
    border: 1px solid rgba(62, 116, 247, 0.7);
    cursor: pointer;

    .btnText {
      flex: 0 0 auto;
      margin-right: 5px;
      height: 21px;
      font-size: 16px;
      color: #3e74f7;
      line-height: 21px;
    }

    .arrowIcon {
      flex: 0 0 auto;
      width: 18px;
      height: 18px;
      background-image: url('./svg/groupCreateArrow.svg');
      background-size: cover;
      background-repeat: no-repeat;
      box-sizing: border-box;
    }
  }

  .description {
    width: 300px;
    height: 19px;
    font-size: 14px;
    color: #666;
    line-height: 19px;
    text-align: center;
  }
}
