/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-07-30
 * @Description  : AI-秒圈【人数预估】卡片
 */
import React from 'react';
import _ from 'lodash';
import { thousandFormat } from '../../util';

import styles from './index.less';

interface IProps {
  data:any;
}
const MiaoQuanCustCount:React.FC<IProps> = (props) => {
  const { data } = props;

  // 圈中客户数
  const custCount = thousandFormat(data?.custCount || 0, true, ',', false);
  // 圈中客户占比
  const custPercent = _.isNumber(data?.custPercent) ? (data?.custPercent * 100).toFixed(2) : '0';

  return (
    <div className={styles.miaoquanCustCount}>
      <div className={styles.itemCard}>
        <div className={styles.custCountLine}>
          <div className={styles.custCount}>{custCount}</div>
          <div className={styles.custCountUnit}>人</div>
        </div>
        <div className={styles.custCountTitle}>圈中客户数</div>
      </div>
      <div className={styles.itemCard}>
        <div className={styles.custPercentLine}>
          <div className={styles.custPercent}>{custPercent}</div>
          <div className={styles.custPercentUnit}>%</div>
        </div>
        <div className={styles.custPercentTitle}>圈中客户占比</div>
      </div>
    </div>
  );
};


export default React.memo(MiaoQuanCustCount);
