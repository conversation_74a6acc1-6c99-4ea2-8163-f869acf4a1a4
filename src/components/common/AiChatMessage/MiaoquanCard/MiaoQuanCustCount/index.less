.miaoquanCustCount {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-top: 10px;
  margin-bottom: 30px;
  max-width: 800px;
  min-width: 458px;
  height: 78px;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;

  .itemCard {
    flex: 1 1 auto;
    padding: 15px 0 0 20px;
    height: 78px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    overflow: hidden;
    box-sizing: border-box;

    .custCountLine {
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;
      height: 24px;

      .custCount {
        flex: 0 0 auto;
        margin-right: 4px;
        height: 24px;
        font-size: 18px;
        font-weight: bold;
        color: #108ee9;
        line-height: 24px;
      }

      .custCountUnit {
        flex: 0 0 auto;
        height: 19px;
        font-size: 14px;
        color: #108ee9;
        line-height: 19px;
      }
    }

    .custCountTitle {
      margin-top: 5px;
      height: 19px;
      font-size: 14px;
      color: #108ee9;
      line-height: 19px;
    }

    .custPercentLine {
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;
      height: 24px;

      .custPercent {
        flex: 0 0 auto;
        margin-right: 4px;
        height: 24px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
        line-height: 24px;
      }

      .custPercentUnit {
        flex: 0 0 auto;
        height: 19px;
        font-size: 14px;
        color: #333;
        line-height: 19px;
      }
    }

    .custPercentTitle {
      margin-top: 5px;
      height: 19px;
      font-size: 14px;
      color: #999;
      line-height: 19px;
    }
  }
}
