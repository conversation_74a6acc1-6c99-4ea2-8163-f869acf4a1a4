/*
 * @Author: sunweibin
 * @Date: 2023-05-15 14:33:03
 * @description 基础FilterModel
 */
import { autobind } from 'core-decorators';
import { FilterRelation } from './config';

class BaseModel {
  constructor(id = '', name = '') {
    // 标签ID
    this.id = id;
    // 标签名称
    this.name = name;
    // 数据模型类型
    this.type = '';
    // 标签的描述
    this.desc = '';
    // 标签值
    this.value = [];
    // 是否下线标签
    this.offline = false;
    // 是否作为【多维标签】的维度子标签
    this.isDimensionLabel = false;
    // 用于控制筛选标签是否可见
    this.visible = true;
    // 用于控制筛选标签是否可用
    this.disabled = false;
    // 用于控制是否展开下拉框
    this.popoverOpen = false;
    // 标签的关系
    this.relation = FilterRelation.COMMON001;
    // 单位
    this.unit = '';
    // 单位系数
    this.unitCoe = 1;
    // 标签数据最新更新的时间戳
    this.modifyTimeStamp = 0;
  }

  @autobind
  setModifyTimeStamp() {
    this.modifyTimeStamp = Date.now();
  }

  // 设置标签作为维度标签
  @autobind
  setSubDimension() {
    this.isDimensionLabel = true;
    return this;
  }

  // 判断标签是否维度标签
  @autobind
  isSubDimension() {
    return this.isDimensionLabel;
  }

  @autobind
  setOffLine(offline) {
    this.offline = offline;
    return this;
  }

  @autobind
  getOffLine() {
    return this.offline;
  }

  @autobind
  setUnit(unit, unitCoe) {
    this.unit = unit;
    this.unitCoe = this.predicateUnitCoe(unitCoe) ? 1 : Number(unitCoe);

    return this;
  }

  @autobind
  getUnit() {
    return this.unit;
  }

  @autobind
  predicateUnitCoe(val) {
    return val === '' || val == null;
  }

  @autobind
  getId() {
    return this.id;
  }

  @autobind
  setId(id) {
    this.id = id;

    return this;
  }

  @autobind
  getName() {
    return this.name;
  }

  @autobind
  setName(name) {
    this.name = name;

    return this;
  }

  @autobind
  getType() {
    return this.type;
  }

  @autobind
  setType(type) {
    this.type = type;

    return this;
  }

  @autobind
  getDesc() {
    return this.desc;
  }

  @autobind
  setDesc(desc) {
    this.desc = desc;

    return this;
  }

  // PC端的从后端过来的数据需要经过解析后
  @autobind
  parseValue() {
    return this.setValue([]);
  }

  @autobind
  setValue(value) {
    this.setModifyTimeStamp();
    this.value = value;

    return this;
  }

  @autobind
  getValue() {
    return this.value;
  }

  @autobind
  setVisible(visible = true) {
    this.visible = visible;

    return this;
  }

  @autobind
  getVisible() {
    return this.visible;
  }

  @autobind
  setDisabled(disabled = true) {
    this.disabled = disabled;

    return this;
  }

  @autobind
  getDisabled() {
    return this.disabled;
  }

  @autobind
  setPopOverOpen(popoverOpen = true) {
    this.popoverOpen = popoverOpen;

    return this;
  }

  @autobind
  getPopOverOpen() {
    return this.popoverOpen;
  }

  @autobind
  setRelation(relation) {
    this.relation = relation;

    return this;
  }

  @autobind
  getRelation() {
    return this.relation;
  }

  // 子类DataModel需要重写该方法体
  @autobind
  getApiValueInRule() {
    return '';
  }

  // 子类DataModel需要重写该方法体
  @autobind
  getApiValueInDimension() {
    return '';
  }

  // 子类DataModel需要重写该方法体
  @autobind
  isEmpty() {
    return true;
  }

  // 子类DataModel需要重写该方法体
  @autobind
  getValueLabel() {
    return '';
  }

  // 子类DataModel需要重写该方法体
  @autobind
  getCustListLabel() {
    return '';
  }

  // 子类DataModel需要重写该方法体, 多维标签不需要重写该方法
  @autobind
  getDimensionLabel() {
    return '';
  }

  @autobind
  setMeta(data) {
    this.setId(data.filterId);
    this.setName(data.filterName);
    this.setDesc(data.filterDesc);
    this.setUnit(data.unit, data.unitCoe);
    this.setOffLine(data.offline || false);

    return this;
  }

  // 子类DataModel需要重写该方法体
  @autobind
  isCorrect() {
    return true;
  }

  @autobind
  getFilterUpdateTimeStamp() {
    return {
      filterId: this.id,
      modifyTimeStamp: this.modifyTimeStamp,
    };
  }

  @autobind
  getFilterIdAndValue() {
    return {
      filterId: this.id,
      filterValue: this.value,
    };
  }

  @autobind
  getFilterMeta() {
    return {
      filterId: this.id,
      filterName: this.name,
      filterType: this.type,
      filterDesc: this.desc,
      relation: this.relation,
      unit: this.unit,
      unitCoe: this.unitCoe,
    };
  }

  // 基础标签的数据
  @autobind
  getLabelFilterData() {
    const filterMeta = this.getFilterMeta();

    return {
      ...filterMeta,
      disabled: this.disabled,
      offline: this.offline,
      filterValue: this.value,
    };
  }
}

export default BaseModel;
