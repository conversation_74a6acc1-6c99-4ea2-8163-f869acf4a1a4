/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable no-template-curly-in-string */
/*
 * @Author: sunweibin
 * @Date: 2023-05-16 09:37:05
 * @description 日期区间单选标签FilterModel
 */

import { autobind } from 'core-decorators';
import _ from 'lodash';

import BaseModel from '../BaseModel';
import {
  DATE_RANG_SPECIFY,
  DATE_RANGE_DYNAMIC,
  DYNAMIC_NATURAL,
  DYNAMIC_TRANSACTION,
  DYNAMIC_RANGE_OPTIONS,
  DYNAMIC_WEEK,
  DYNAMIC_MONTH,
  DYNAMIC_SEASON,
  DYNAMIC_YEAR,
} from '../config';

class DateRangeSingleFilterModel extends BaseModel {
  constructor(id, name) {
    super(id, name);

    // 【日期区间单选】标签类型固定值
    this.type = 4;
    // 【日期区间】当前存在【具体时间段】【动态时间段】两种类型
    // 因此用户填写的数据需要区分当前的类型
    this.value = {
      // 默认为【具体时间段(SPECIFY_RANGE)】、【动态时间段(DYNAMIC_RANGE)】
      type: DATE_RANG_SPECIFY,
      value: [],
    };
  }

  @autobind
  clearValue() {
    this.value = {
      type: DATE_RANG_SPECIFY,
      value: [],
    };
  }

  // 初始化场景的时候转化ES数据为自己的数据格式
  @autobind
  parseValueES(esValue, esValueLabel) {
    const [name] = _.split(esValueLabel, ':');
    this.name = name;
    this.value = this.parseDateES(esValue);
  }

  @autobind
  parseDateES(esDate) {
    if (_.startsWith(esDate, '$')) {
      // 以 $ 开头为动态时间段
      return this.parseDynamicDateES(esDate);
    }

    return this.parseSpecifyDateES(esDate);
  }

  @autobind
  parseDynamicDateES(esDate) {
    return {
      type: DATE_RANGE_DYNAMIC,
      value: [this.covertToDynamicDateValueES(esDate)],
    };
  }

  @autobind
  covertToDynamicDateValueES(esDate) {
    if (_.includes(esDate, 'current_week_monday')) {
      // return '${calTime(.now,\'current_week_monday\',0,\'yyyyMMdd\')}';
      return {
        key: DYNAMIC_WEEK,
        N: null,
      };
    }

    if (_.includes(esDate, 'current_month_firstday')) {
      return {
        key: DYNAMIC_MONTH,
        N: null,
      };
    }

    if (_.includes(esDate, 'current_season_firstday')) {
      return {
        key: DYNAMIC_SEASON,
        N: null,
      };
    }

    if (_.includes(esDate, 'current_year_firstday')) {
      return {
        key: DYNAMIC_YEAR,
        N: null,
      };
    }
    // '${calTime(.now,\'recent_deal_day\',N,\'yyyyMMdd\')}'.replace('N', N);
    // NOTE: 这一步必须写在 day【近N个自然日】 判断之前
    if (_.includes(esDate, 'recent_deal_day')) {
      const esJiaoBenArray = _.split(esDate, ',');
      // 数值解析后存在于数组的第三项里面
      const stringN = _.nth(esJiaoBenArray, 2);
      return {
        key: DYNAMIC_TRANSACTION,
        N: Math.abs(stringN),
      };
    }

    //  '${calTime(.now,\'day\',-N,\'yyyyMMdd\')}'.replace('N', N);
    if (_.includes(esDate, 'day')) {
      const esJiaoBenArray = _.split(esDate, ',');
      // 数值解析后存在于数组的第三项里面
      const stringN = _.nth(esJiaoBenArray, 2);
      return {
        key: DYNAMIC_NATURAL,
        N: Math.abs(stringN),
      };
    }

    return {};
  }

  @autobind
  parseSpecifyDateES(esDate) {
    return {
      type: DATE_RANG_SPECIFY,
      value: _.split(esDate, ','),
    };
  }

  @autobind
  getApiValueInSpecial(value) {
    return {
      type: DATE_RANG_SPECIFY,
      value: _.join(value, ','),
    };
  }

  @autobind
  getApiValueInDynamic(value) {
    return {
      type: DATE_RANGE_DYNAMIC,
      value,
    };
  }

  @autobind
  splicingDynamicScript(value) {
    const { key, N } = value;

    if (key === DYNAMIC_WEEK) {
      return '${calTime(.now,\'current_week_monday\',0,\'yyyyMMdd\')}';
    }

    if (key === DYNAMIC_MONTH) {
      return '${calTime(.now,\'current_month_firstday\',0,\'yyyyMMdd\')}';
    }

    if (key === DYNAMIC_SEASON) {
      return '${calTime(.now,\'current_season_firstday\',0,\'yyyyMMdd\')}';
    }

    if (key === DYNAMIC_YEAR) {
      return '${calTime(.now,\'current_year_firstday\',0,\'yyyyMMdd\')}';
    }

    if (key === DYNAMIC_NATURAL) {
      return '${calTime(.now,\'day\',-N,\'yyyyMMdd\')}'.replace('N', N);
    }

    if (key === DYNAMIC_TRANSACTION) {
      return '${calTime(.now,\'recent_deal_day\',N,\'yyyyMMdd\')}'.replace('N', N);
    }

    return '';
  }

  // NOTE: 临时将日期数据的格式转换成PC端统一的脚本字符串，后面直接删除使用新的替换
  @autobind
  transferToPreviousApiValue() {
    const { type, value } = this.value;
    // 【具体时间段】为字符串数组
    if (type === DATE_RANG_SPECIFY) {
      return _.join(value, ',');
    }

    if (type === DATE_RANGE_DYNAMIC) {
      const dynamicValue = _.head(value);
      return this.splicingDynamicScript(dynamicValue);
    }

    return '';
  }

  // NOTE: 未来可能使用的数据格式
  @autobind
  transferToFutureApiValue() {
    const { type, value } = this.value;

    if (type === DATE_RANGE_DYNAMIC) {
      return this.getApiValueInDynamic(value);
    }

    if (type === DATE_RANG_SPECIFY) {
      return this.getApiValueInSpecial(value);
    }

    return {
      type: DATE_RANG_SPECIFY,
      value: [],
    };
  }

  // 外部调用，勿删
  // @see src\components\CircleCustComponents\FilterLabels\DateRangeSingleFilterLabel\api_params.js
  @autobind
  getApiValue() {
    if (this.isEmpty()) {
      return null;
    }
    // TODO: 如果后期需要修改日期区间的数据格式, 可以直接改用 this.transferToFutureApiValue();
    return this.transferToPreviousApiValue();
  }

  @autobind
  isCorrectInSpecify(value) {
    if (_.every(value, this.predicateFull)) {
      // 全有值也可以
      return true;
    }

    return false;
  }

  @autobind
  predicateN(val) {
    const { N, key } = val;
    if (_.isNil(N) || N === '') {
      return false;
    }

    const degitN = _.toNumber(N);
    if (key === DYNAMIC_NATURAL) {
      // 【近N个自然日】为 0 ~ 1095
      return degitN >= 0 && degitN <= 1095;
    }

    if (key === DYNAMIC_TRANSACTION) {
      // 【近N个交易日】为 1 ~ 1095
      return degitN >= 1 && degitN <= 1095;
    }

    return true;
  }

  @autobind
  isCorrectInDynamic(value) {
    const dynamicVal = _.head(value);
    // 【近N个自然日】【近N个交易日】需要校验输入的 N 值，不能为空
    const needJudgeN = [DYNAMIC_NATURAL, DYNAMIC_TRANSACTION];
    if (_.includes(needJudgeN, dynamicVal?.key)) {
      return this.predicateN(dynamicVal);
    }

    if (!_.isEmpty(dynamicVal?.key)) {
      return true;
    }

    return false;
  }

  // 判断数据填写是否正确// 外部调用。勿删
  @autobind
  isCorrect() {
    if (this.isEmpty()) {
      // 可以全空
      return true;
    }

    const { type, value } = this.value;
    if (type === DATE_RANG_SPECIFY) {
      return this.isCorrectInSpecify(value);
    }

    if (type === DATE_RANGE_DYNAMIC) {
      return this.isCorrectInDynamic(value);
    }

    return false;
  }

  @autobind
  predicateEmpty(val) {
    return val === '' || val == null;
  }

  @autobind
  predicateFull(val) {
    if (Array.isArray(val)) {
      return _.every(val, this.predicateFull);
    }

    return !_.isEmpty(val);
  }

  // 因为目前存在【具体时间段】、【动态时间段】所以需要区分开判断
  @autobind
  isEmptyInSpecifyRange(value) {
    if (_.isEmpty(value)) {
      return true;
    }

    if (_.every(value, this.predicateEmpty)) {
      return true;
    }

    return false;
  }

  @autobind
  isEmptyInDynamicRange(value) {
    if (_.isEmpty(value)) {
      return true;
    }

    // 【动态时间段】的value里面是一个[{ key, N }]这个结构,而且目前只有一个值
    // key必须要有值
    const dynamicValue = _.head(value);
    if (_.isEmpty(dynamicValue)) {
      return true;
    }

    return false;
  }

  // 外部调用。勿删
  @autobind
  isEmpty() {
    const { type, value } = this.value;
    if (type === DATE_RANG_SPECIFY) {
      return this.isEmptyInSpecifyRange(value);
    }

    if (type === DATE_RANGE_DYNAMIC) {
      return this.isEmptyInDynamicRange(value);
    }

    return false;
  }

  @autobind
  getValueLabelInSpecifyRange() {
    const { value } = this.value;
    const rangeLabel = value.join('~');

    return rangeLabel;
  }

  @autobind
  getValueLabelInDynamicRange() {
    const { value } = this.value;
    const dynamicValue = _.head(value);

    if (dynamicValue?.key === DYNAMIC_NATURAL) {
      return `近${dynamicValue?.N}个自然日`;
    }

    if (dynamicValue?.key === DYNAMIC_TRANSACTION) {
      return `近${dynamicValue?.N}个交易日`;
    }

    const option = _.find(DYNAMIC_RANGE_OPTIONS, item => item.value === dynamicValue?.key);

    return option.label;
  }

  // 外部调用 -- 用于规则相关的数据
  @autobind
  getApiValueInRule() {
    return {
      stdInterfacePropertyId: this.relation,
      children: [
        {
          stdInterfacePropertyId: this.id,
          value: this.getApiValue(),
          valueLabel: this.getValueLabel(),
        },
      ],
    };
  }

  // 外部调用 -- 用于作为多维标签的子标签数据
  @autobind
  getApiValueInDimension() {
    return {
      stdInterfacePropertyId: this.id,
      value: this.getApiValue(),
      valueLabel: this.getValueLabel(),
    };
  }

  // 外部调用 -- 客户列表调用
  @autobind
  getCustListLabel() {
    // 在客户列表页面，如果选择了不限，则只展示标签名
    if (this.isEmpty()) {
      return this.name;
    }

    return this.getValueLabel();
  }

  // 给外部展示筛选标签内容
  @autobind
  getValueLabel() {
    // 在PC端基础类型的标签下如果没有选择值，则全部展示【不限】
    // 如果本标签作为多维标签的子标签则默认展示【请选择】
    let labelText = this.isSubDimension() ? '请选择' : '不限';
    const { type } = this.value;

    if (!this.isEmpty() && type === DATE_RANGE_DYNAMIC) {
      labelText = this.getValueLabelInDynamicRange();
    }

    if (!this.isEmpty() && type === DATE_RANG_SPECIFY) {
      labelText = this.getValueLabelInSpecifyRange();
    }

    return labelText;
  }

  // 用于多维标签获取其子标签的label展示
  @autobind
  getDimensionLabel() {
    if (this.isEmpty()) {
      return '';
    }

    let labelText = '';

    const { type } = this.value;

    if (type === DATE_RANGE_DYNAMIC) {
      labelText = this.getValueLabelInDynamicRange();
    }

    if (type === DATE_RANG_SPECIFY) {
      labelText = this.getValueLabelInSpecifyRange();
    }

    return labelText;
  }
}

export default DateRangeSingleFilterModel;
