/*
 * @Author: sunweibin
 * @Date: 2023-05-15 15:25:54
 * @description 是否类FilterModel
 */
import { autobind } from 'core-decorators';
import _ from 'lodash';

import { AORTA_N_VALUE, AORTA_Y_VALUE } from '../config';
import BaseModel from '../BaseModel';

class YorNFilterModel extends BaseModel {
  constructor(id, name) {
    super(id, name);

    // 标签类型固定值
    this.type = 1;
    // 是否类标签的值
    this.value = [];
  }

  // 初始化场景的时候转化ES数据为自己的数据格式
  @autobind
  parseValueES(esValue, esValueLabel) {
    // 1. 需要将 esValue 组装成 [{ value: esValue, label: '是'}] 这种格式
    this.value = this.convertValueES(esValue);
    this.name = _.split(esValueLabel, ':')[0];
  }

  @autobind
  convertValueES(esValue) {
    if (esValue === AORTA_Y_VALUE) {
      return [{ value: AORTA_Y_VALUE, label: '是' }];
    }

    if (esValue === AORTA_N_VALUE) {
      return [{ value: AORTA_N_VALUE, label: '否' }];
    }

    return [{ value: '', label: '不限' }];
  }

  @autobind
  clearValue() {
    this.value = [];
  }

  // 接口中传递是否类标签的值
  @autobind
  getApiValue() {
    if (this.isEmpty()) {
      return null;
    }

    return _.head(this.value)?.value;
  }

  // 外部调用 -- 主要是给规则相关使用
  @autobind
  getApiValueInRule() {
    return {
      stdInterfacePropertyId: this.relation,
      // 在规则相关的接口中，此处children存放标签选择的具体值，【是否类】标签id,value是反着来的
      children: [{
        stdInterfacePropertyId: this.getApiValue(),
        value: this.id,
        valueLabel: this.getValueLabel(),
      }],
    };
  }

  // 外部调用 -- 主要是给多维标签中获取子标签的数据使用
  @autobind
  getApiValueInDimension() {
    return {
      stdInterfacePropertyId: this.getApiValue(),
      value: this.id,
      valueLabel: this.getValueLabel(),
    };
  }

  // 外部调用
  @autobind
  isEmpty() {
    const option = _.head(this.value);
    return _.isEmpty(option) || option?.value === '' || option?.value === null;
  }

  // 外部调用
  @autobind
  isCorrect() {
    return true;
  }

  // 外部调用 -- 客户列表调用
  @autobind
  getCustListLabel() {
    // 在客户列表页面，如果选择了不限，则只展示标签名
    if (this.isEmpty()) {
      return this.name;
    }

    return this.getValueLabel();
  }

  // 给外部展示筛选标签内容
  @autobind
  getValueLabel() {
    // 在PC端基础类型的标签下如果没有选择值，则全部展示【不限】
    // 如果本标签作为多维标签的子标签则默认展示【请选择】
    let labelText = this.isSubDimension() ? '请选择' : '不限';

    const currentValue = _.head(this.value);
    if (currentValue?.value === AORTA_Y_VALUE) {
      labelText = '是';
    }
    if (currentValue?.value === AORTA_N_VALUE) {
      labelText = '否';
    }

    return labelText;
  }

  // 用于多维标签获取其子标签的label展示
  @autobind
  getDimensionLabel() {
    const currentValue = _.head(this.value);
    if (currentValue?.value === AORTA_Y_VALUE) {
      return '是';
    }

    if (currentValue?.value === AORTA_N_VALUE) {
      return '否';
    }

    return '';
  }
}

export default YorNFilterModel;
