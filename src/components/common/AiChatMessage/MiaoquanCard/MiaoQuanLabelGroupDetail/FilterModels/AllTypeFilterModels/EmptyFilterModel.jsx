/*
 * @Author: sunweibin
 * @Date: 2023-05-15 15:25:54
 * @description 是否类FilterModel
 */
import { autobind } from 'core-decorators';

import BaseModel from '../BaseModel';

class EmptyFiterModel extends BaseModel {
  constructor(id, name) {
    super(id, name);

    // 空标签id为null
    this.id = null;
    // 标签类型固定值
    this.type = 'Empty';
    this.value = null;
  }

  @autobind
  clearValue() {
    this.value = null;
  }

  // 初始化场景的时候转化ES数据为自己的数据格式
  @autobind
  parseValueES() {
    // 空标签不需要操作
  }

  // 外部调用 -- 主要是给规则相关使用
  @autobind
  getApiValueInRule() {
    return {
      stdInterfacePropertyId: this.relation,
      // 在规则相关的接口中，此处children存放标签选择的具体值，【是否类】标签id,value是反着来的
      children: [
        {
          stdInterfacePropertyId: this.value,
          value: this.id,
          valueLabel: this.getValueLabel(),
        },
      ],
    };
  }

  // 外部调用 -- 主要是给多维标签中获取子标签的数据使用
  @autobind
  getApiValueInDimension() {
    return {
      stdInterfacePropertyId: this.value,
      value: this.id,
      valueLabel: this.getValueLabel(),
    };
  }

  // 外部调用
  @autobind
  isEmpty() {
    return true;
  }

  // 外部调用
  @autobind
  isCorrect() {
    return true;
  }

  // 给外部展示筛选标签内容
  @autobind
  getValueLabel() {
    return '--';
  }
}

export default EmptyFiterModel;
