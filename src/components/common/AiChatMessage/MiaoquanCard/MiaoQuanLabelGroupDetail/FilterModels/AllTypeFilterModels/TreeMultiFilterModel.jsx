/*
 * @Author: sunweibin
 * @Date: 2022-10-28 11:07:17
 * @description 树形多选标签数据模型定义
 */

import { autobind } from 'core-decorators';
import _ from 'lodash';

import BaseModel from '../BaseModel';

class TreeMultiFilterModel extends BaseModel {
  constructor(id, name) {
    super(id, name);

    // 【树形多选标签】标签类型固定值
    this.type = 13;
    // 用户填写的标签的值
    this.value = [];
  }

  @autobind
  clearValue() {
    this.value = [];
  }

  // 初始化场景的时候转化ES数据为自己的数据格式
  @autobind
  parseValueES(esValue, esValueLabel) {
    const [name, labelText] = _.split(esValueLabel, ':');
    this.name = name;
    const labels = _.split(labelText, ',');
    this.value = this.parseOption(esValue, labels);
  }

  @autobind
  parseOption(values, labels) {
    return _.reduce(values, (result, val, i) => {
      const option = {
        value: val,
        label: _.trim(labels[i]),
      };

      return [...result, option];
    }, []);
  }

  @autobind
  getApiValue() {
    if (this.isEmpty()) {
      return null;
    }

    return _.map(this.value, item => item.value);
  }

  @autobind
  isCorrect() {
    // 代表不需要校验
    return true;
  }

  @autobind
  predicateEmpty(option) {
    return _.isEmpty(option) || option?.value === '' || option?.value === null;
  }

  @autobind
  isEmpty() {
    if (_.isEmpty(this.value)) {
      return true;
    }

    if (_.every(this.value, this.predicateEmpty)) {
      return true;
    }

    return false;
  }

  // 外部调用 -- 用于规则相关的数据
  @autobind
  getApiValueInRule() {
    return {
      stdInterfacePropertyId: this.relation,
      children: [
        {
          stdInterfacePropertyId: this.id,
          value: this.getApiValue(),
          valueLabel: this.getValueLabel(),
        },
      ],
    };
  }

  // 外部调用 -- 用于作为多维标签的子标签数据
  @autobind
  getApiValueInDimension() {
    return {
      stdInterfacePropertyId: this.id,
      value: this.getApiValue(),
      valueLabel: this.getValueLabel(),
    };
  }

  // 外部调用 -- 客户列表调用
  @autobind
  getCustListLabel() {
    // 在客户列表页面，如果选择了不限，则只展示标签名
    if (this.isEmpty()) {
      return this.name;
    }

    return this.getValueLabel();
  }

  // 给外部展示筛选标签内容
  @autobind
  getValueLabel() {
    // 在PC端基础类型的标签下如果没有选择值，则全部展示【不限】
    // 如果本标签作为多维标签的子标签则默认展示【请选择】
    let labelText = this.isSubDimension() ? '请选择' : '不限';

    if (!this.isEmpty()) {
      labelText = _.map(this.value, item => item.label).join('、');
    }

    return labelText;
  }

  // 用于多维标签获取其子标签的label展示
  @autobind
  getDimensionLabel() {
    if (this.isEmpty()) {
      return '';
    }

    const labelText = _.map(this.value, item => item.label).join('、');
    return labelText;
  }
}

export default TreeMultiFilterModel;
