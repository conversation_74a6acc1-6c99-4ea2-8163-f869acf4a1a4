/*
 * @Author: sunweibin
 * @Date: 2023-05-16 09:18:47
 * @description 数值区间单选标签 FilterModel
 */

import { autobind } from 'core-decorators';
import _ from 'lodash';
import NP from 'number-precision';

import BaseModel from '../BaseModel';

class NumberRangeSingleFilterModel extends BaseModel {
  constructor(id, name) {
    super(id, name);

    // 【数值区间单选】标签类型固定值
    this.type = 2;
    // 用户填写的标签的值
    this.value = [];
  }

  @autobind
  clearValue() {
    this.value = [];
  }

  // 初始化场景的时候转化ES数据为自己的数据格式
  @autobind
  parseValueES(esValue, esValueLabel) {
    const [name] = _.split(esValueLabel, ':');
    this.name = name;

    this.value = this.parseSingleValueES(esValue);
  }

  @autobind
  parseSingleValueES(esValue) {
    const [minVal, maxVal] = _.split(esValue, ',');
    // 数值区间，first
    // 因为存在允许min, max其中一个不填写值的情况，所以此处需要做下特殊处理
    let min = null;
    let max = null;

    if (!this.predicateEmpty(minVal)) {
      min = NP.divide(_.toNumber(minVal), this.unitCoe);
    }

    if (!this.predicateEmpty(maxVal)) {
      max = NP.divide(_.toNumber(maxVal), this.unitCoe);
    }

    return [min, max];
  }

  @autobind
  getApiValue() {
    if (this.isEmpty()) {
      return null;
    }

    return _.map(this.value, (item) => {
      if (!this.predicateEmpty(item)) {
        return NP.times(_.toNumber(item), this.unitCoe);
      }

      return '';
    }).join(',');
  }

  @autobind
  isCorrectSingleRange(dataRange) {
    const [min, max] = dataRange;
    // 全空可以
    if (this.predicateEmpty(min) && this.predicateEmpty(max)) {
      return true;
    }
    if (this.predicateEmpty(min) || this.predicateEmpty(max)) {
      // 有一个是空也可以
      return true;
    }
    // 最小值必须小于等于最大值
    if (Number(min) <= Number(max)) {
      return true;
    }

    return false;
  }

  // 判断数据填写是否正确
  @autobind
  isCorrect() {
    // 数值区间，可以只有一个值，但是最大值必须大于等于最小值
    if (this.isEmpty()) {
      // 可以全空
      return true;
    }

    if (this.isCorrectSingleRange(this.value)) {
      return true;
    }

    return false;
  }

  @autobind
  predicateEmpty(val) {
    return val === '' || val == null;
  }

  @autobind
  isEmpty() {
    if (_.isEmpty(this.value)) {
      return true;
    }

    if (_.every(this.value, this.predicateEmpty)) {
      return true;
    }

    return false;
  }

  @autobind
  getSingleRangeValueLabel() {
    const [minVal, maxVal] = this.value;
    if (minVal === maxVal) {
      return `= ${maxVal}${this.unit}`;
    }

    if (this.predicateEmpty(minVal) && !this.predicateEmpty(maxVal)) {
      return `≤ ${maxVal}${this.unit}`;
    }

    if (!this.predicateEmpty(minVal) && this.predicateEmpty(maxVal)) {
      return `≥ ${minVal}${this.unit}`;
    }

    if (!this.predicateEmpty(minVal) && !this.predicateEmpty(maxVal)) {
      return `${minVal}${this.unit} ~ ${maxVal}${this.unit}`;
    }

    return '';
  }

  // 外部调用 -- 用于规则相关的数据
  @autobind
  getApiValueInRule() {
    return {
      stdInterfacePropertyId: this.relation,
      children: [
        {
          stdInterfacePropertyId: this.id,
          value: this.getApiValue(),
          valueLabel: this.getValueLabel(),
        },
      ],
    };
  }

  // 外部调用 -- 用于作为多维标签的子标签数据
  @autobind
  getApiValueInDimension() {
    return {
      stdInterfacePropertyId: this.id,
      value: this.getApiValue(),
      valueLabel: this.getValueLabel(),
    };
  }

  // 外部调用 -- 客户列表调用
  @autobind
  getCustListLabel() {
    // 在客户列表页面，如果选择了不限，则只展示标签名
    if (this.isEmpty()) {
      return this.name;
    }

    return this.getValueLabel();
  }

  // 给外部展示筛选标签内容
  @autobind
  getValueLabel() {
    // 在PC端基础类型的标签下如果没有选择值，则全部展示【不限】
    // 如果本标签作为多维标签的子标签则默认展示【请选择】
    let labelText = this.isSubDimension() ? '请选择' : '不限';

    if (!this.isEmpty()) {
      labelText = this.getSingleRangeValueLabel();
    }

    return labelText;
  }

  // 用于多维标签获取其子标签的label展示
  @autobind
  getDimensionLabel() {
    if (this.isEmpty()) {
      return '';
    }

    const labelText = this.getSingleRangeValueLabel();
    return labelText;
  }
}

export default NumberRangeSingleFilterModel;
