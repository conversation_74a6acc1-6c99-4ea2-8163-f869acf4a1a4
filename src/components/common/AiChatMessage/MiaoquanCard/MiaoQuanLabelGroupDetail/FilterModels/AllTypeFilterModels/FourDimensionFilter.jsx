/*
 * @Author: sunweibin
 * @Date: 2023-05-24 15:45:22
 * @description 四维标签的FilterModel
 */
import _ from 'lodash';
import { autobind } from 'core-decorators';

import BaseModel from '../BaseModel';

class FourDimensionFilter extends BaseModel {
  constructor(id, name) {
    super(id, name);

    // 四维标签的ID
    this.id = id;
    // 【四维标签】的标签类型固定值
    this.type = 17;
    // 四维标签中 value 存放的应该是子标签的 filterModel
    this.value = [];
  }

  // 重写了 BaseModel 的方法
  // 四维标签由于其this.value保存的是子标签的 filterModel,所以需要特殊处理
  @autobind
  getLabelFilterData() {
    const filterMeta = this.getFilterMeta();

    return {
      ...filterMeta,
      disabled: this.disabled,
      filterValue: _.map(this.value, item => item.getLabelFilterData()),
    };
  }

  /**
   * 重写了 BaseModel 的方法
   * 四维标签由于其this.value保存的是子标签的 filterModel,所以需要特殊处理
   */
  @autobind
  getFilterIdAndValue() {
    return {
      filterId: this.id,
      filterValue: _.map(this.value, item => item.getFilterIdAndValue()),
    };
  }

  /**
   *  多维标签的清空数据，为清空子标签选择的数据
   */
  @autobind
  clearValue() {
    return _.map(this.value, subFilter => subFilter.clearValue());
  }

  // 初始化场景的时候转化ES数据为自己的数据格式
  @autobind
  parseValueLabelES(esValueLabel) {
    const [name] = _.split(esValueLabel, ':');
    this.name = name;
  }

  @autobind
  isCorrect() {
    // PC端因为在面板处点击【确定】时就校验了，所以在 FilterModel 处不做任何额外判断
    return !this.isEmpty();
  }

  @autobind
  predicateEmpty(subFilter) {
    return subFilter.isEmpty();
  }

  @autobind
  isEmpty() {
    if (_.isEmpty(this.value)) {
      return true;
    }

    if (_.some(this.value, this.predicateEmpty)) {
      return true;
    }

    return false;
  }

  /**
   * 根据子标签ID查找当前多维标签下的子标签FilterModel
   * @param {String} filterId 多维标签下的子标签ID
   */
  @autobind
  findSubFilterById(subFilterId) {
    return _.find(this.value, subFilter => subFilter.getId() === subFilterId);
  }

  /**
   * 用于在多维标签面板渲染子标签
   */
  @autobind
  getSubFitlersDisplayData() {
    return _.map(this.value, subFilter => subFilter.getFilterMeta());
  }

  /**
   * 获取子标签的value值，用于重置恢复
   */
  @autobind
  getSubFiltersValue() {
    return _.map(this.value, (subFilter) => {
      const filterId = subFilter.getId();
      const filterValue = subFilter.getValue();

      return {
        filterId,
        filterValue,
      };
    });
  }

  /**
   * 获取用于四维标签神策日志的数据
   */
  @autobind
  getSensorLogData() {
    const subFilters = _.map(this.value, subFilter => subFilter.getValueLabel());

    return {
      filterId: this.id,
      filterName: this.name,
      filterValue: subFilters,
    };
  }

  @autobind
  resetSubFiltersValue(subFiltersValue) {
    _.forEach(subFiltersValue, (item) => {
      const subFilter = this.findSubFilterById(item.filterId);
      subFilter.setValue(item.filterValue);
    });
  }

  // 用于外部校验子哪些子维度标签没有填值
  @autobind
  getEmptySubFilter() {
    return _.reduce(this.value, (result, subFilter) => {
      if (subFilter.isEmpty()) {
        return [...result, subFilter.getFilterMeta()];
      }

      return result;
    }, []);
  }

  @autobind
  checkSubFilters() {
    const emptySubFilters = this.getEmptySubFilter();
    if (!_.isEmpty(emptySubFilters)) {
      this.emitSubfilterCheck(emptySubFilters);
    }

    return emptySubFilters;
  }

  @autobind
  getApiValue() {
    if (this.isEmpty()) {
      return [];
    }

    return _.map(this.value, subFilter => subFilter.getApiValueInDimension());
  }

  // 外部调用 -- 用于规则相关的数据
  @autobind
  getApiValueInRule() {
    return {
      stdInterfacePropertyId: this.relation,
      children: [
        {
          stdInterfacePropertyId: this.id,
          value: null,
          children: this.getApiValue(),
          valueLabel: this.getValueLabel(),
        },
      ],
    };
  }

  @autobind
  getCustListLabel() {
    // 在客户列表页面，如果选择了不限，则只展示标签名
    if (this.isEmpty()) {
      return this.name;
    }

    return this.getValueLabel();
  }

  /**
   * 四维标签的 valueLabel 展示为其子标签的数据拼接展示
   */
  @autobind
  getValueLabel() {
    // 因为四维标签不可能再次作为其他多维标签的子标签，因此 labelText 默认为 【不限】
    let labelText = '不限';

    if (!this.isEmpty()) {
      labelText = _.map(this.value, subFilter => subFilter.getDimensionLabel()).join('，');
    }

    return labelText;
  }
}

export default FourDimensionFilter;
