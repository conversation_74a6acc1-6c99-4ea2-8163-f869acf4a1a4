/**
 * 【客户归属-主服务经理】的筛选标签ID
 */
export const MAINSERVICE_FILTER_ID = 'IS_MAIN_SER_MNG';
/**
 * 【客户归属-服务经理】的筛选标签ID
 */
export const SERVICEMNG_FILTER_ID = 'E-C-100004';
/**
 * 【客户归属-部门】的筛选标签ID
 */
export const CUST_ORG_FILTER_ID = 'E-C-000009';
/**
 * 【客户归属-主服务经理职位】的筛选标签ID
 */
export const MAIN_SERVICE_POST_FILTER_ID = 'E-C-400009';
/**
 * 【持仓产品】的筛选ID
 */
export const FIN_PRODUCT_FILTER_ID = 'E-C-300002';
/**
 * 【持仓产品（非省心投）】的筛选ID
 */
export const FIN_PRODUCT_NOT_SXT_FILTER_ID = 'E-C-200289';

// 标签关系的ID
export const FilterRelation = {
  /**
   *  同时满足
   */
  COMMON001: 'COMMON001',
  /**
   * 其中排除
   */
  COMMON002: 'COMMON002',
  /**
   *  或者满足
   */
  COMMON003: 'COMMON003',
};
/**
 * 聊TA【是否类标签】的【是】
 */
export const AORTA_Y_VALUE = 'E-C-000000';
/**
 * 聊TA【是否类标签】的【否】
 */
export const AORTA_N_VALUE = 'E-C-000001';

/**
 * 【具体时间段】Key
 */
export const DATE_RANG_SPECIFY = 'SPECIFY_RANGE';
/**
 * 【动态时间段】key
 */
export const DATE_RANGE_DYNAMIC = 'DYNAMIC_RANGE';
/**
 * 【动态时间段】的分类【本周】key
 */
export const DYNAMIC_WEEK = 'week';
/**
 * 【动态时间段】的分类【本月】key
 */
export const DYNAMIC_MONTH = 'month';
/**
 * 【动态时间段】的分类【本季】key
 */
export const DYNAMIC_SEASON = 'season';
/**
 * 【动态时间段】的分类【本年】key
 */
export const DYNAMIC_YEAR = 'year';
/**
 * 【动态时间段】的分类【近N个自然日】key
 */
export const DYNAMIC_NATURAL = 'natural';
/**
 * 【动态时间段】的分类【近N个交易日】key
 */
export const DYNAMIC_TRANSACTION = 'transaction';
/**
 * 动态时间段分类项
 */
export const DYNAMIC_RANGE_OPTIONS = [
  {
    label: '本周',
    value: DYNAMIC_WEEK,
  },
  {
    label: '本月',
    value: DYNAMIC_MONTH,
  },
  {
    label: '本季',
    value: DYNAMIC_SEASON,
  },
  {
    label: '本年',
    value: DYNAMIC_YEAR,
  },
  {
    label: '近N个自然日',
    value: DYNAMIC_NATURAL,
  },
  {
    label: '近N个交易日',
    value: DYNAMIC_TRANSACTION,
  },
];
/**
 * 二维标签类型: 15
 */
export const TWO_DIMENSION_FILTER_TYPE = 15;
/**
 * 三维标签类型: 16
 */
export const THREE_DIMENSION_FILTER_TYPE = 16;
/**
 * 四维标签类型: 17
 */
export const FOUR_DIMENSION_FILTER_TYPE = 17;
/**
 * 多维标签类型
 */
export const DIMENSION_FILTER_TYPES = [
  TWO_DIMENSION_FILTER_TYPE,
  THREE_DIMENSION_FILTER_TYPE,
  FOUR_DIMENSION_FILTER_TYPE,
];
