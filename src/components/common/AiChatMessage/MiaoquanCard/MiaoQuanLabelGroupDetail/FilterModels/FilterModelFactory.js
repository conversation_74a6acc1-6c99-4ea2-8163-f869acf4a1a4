/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-07-30
 * @Description  : AI-秒圈【圈客方案】中标签Model的工厂函数
 */
import _ from 'lodash';

import BaseModel from './BaseModel';
import DateRangeMultiFilterModel from './AllTypeFilterModels/DateRangeMultiFilterModel';
import DateRangeSingleFilterModel from './AllTypeFilterModels/DateRangeSingleFilterModel';
import DictMultiFilterModel from './AllTypeFilterModels/DictMultiFilterModel';
import DictMultiSearchFilterModel from './AllTypeFilterModels/DictMultiSearchFilterModel';
import DictSingleFilterModel from './AllTypeFilterModels/DictSingleFilterModel';
import DictSingleSearchFilterModel from './AllTypeFilterModels/DictSingleSearchFilterModel';
import KeywordFilterModel from './AllTypeFilterModels/KeywordFilterModel';
import NumberRangeMultiFilterModel from './AllTypeFilterModels/NumberRangeMultiFilterModel';
import NumberRangeSingleFilterModel from './AllTypeFilterModels/NumberRangeSingleFilterModel';
import PropertyMultiSearchFilterModel from './AllTypeFilterModels/PropertyMultiSearchFilterModel';
import PropetrySingleSearchFilterModel from './AllTypeFilterModels/PropetrySingleSearchFilterModel';
import TreeMultiFilterModel from './AllTypeFilterModels/TreeMultiFilterModel';
// import TreeSingleFilterModel from './AllTypeFilterModels/TreeSingleFilterModel';
import YorNFilterModel from './AllTypeFilterModels/YorNFilterModel';
import TwoDimensionFilterModel from './AllTypeFilterModels/TwoDimensionFilterModel';
import ThreeDimensionFilterModel from './AllTypeFilterModels/ThreeDimensionFilterModel';
import FourDimensionFilter from './AllTypeFilterModels/FourDimensionFilter';

import EmptyFilterModel from './AllTypeFilterModels/EmptyFilterModel';

import { DIMENSION_FILTER_TYPES } from './config';

class FilterDateModelFactory {
  /**
   * 用于集中控制所以类型标签的数据模型对象，以便使用时能够统一使用
   */

  // 存放所有根据筛选标签 filterType 或者 filterID 来兜底的 FilterModel 构造器
  DefaultMap = new Map();

  // 默认的 FilterModel 构造器
  addDefaultMap = (idOrType, DataModelConstructor) => {
    this.DefaultMap.set(idOrType, DataModelConstructor);
  }

  // 根据后端返回的标签相关数据生成对应的filterModel对象以便调用相关方法
  getFilterModelByData = (data) => {
    // eslint-disable-next-line max-len
    const FilterModel = this.DefaultMap.get(data.filterId) || this.DefaultMap.get(data.filterType) || BaseModel;
    const filterModel = new FilterModel();

    filterModel.setRelation(data.relation)
      .setId(data.filterId)
      .setName(data.filterName)
      .setUnit(data.unit, data.unitCoe)
      .setDesc(data.filterDesc);

    if (_.includes(DIMENSION_FILTER_TYPES, data.filterType)) {
      // 如果是多维标签，则需要将子标签转化为SubFilterModel
      const subFilterModelList = _.map(data.filterValue, (subItem) => {
        const subFilterModel = this.getFilterModelByData(subItem);

        subFilterModel
          .setId(subItem.filterId)
          .setName(subItem.filterName)
          .setUnit(subItem.unit, subItem.unitCoe)
          .setValue(subItem.filterValue);

        return subFilterModel;
      });

      filterModel.setValue(subFilterModelList);
    } else {
      // 基础标签直接setValue
      filterModel.setValue(data.filterValue);
    }

    return filterModel;
  }
}

const factoryInstance = new FilterDateModelFactory();

// 定义下默认的 FilterModel 构造器
factoryInstance.addDefaultMap(1, YorNFilterModel);
factoryInstance.addDefaultMap(2, NumberRangeSingleFilterModel);
factoryInstance.addDefaultMap(3, NumberRangeMultiFilterModel);
factoryInstance.addDefaultMap(4, DateRangeSingleFilterModel);
factoryInstance.addDefaultMap(5, DateRangeMultiFilterModel);
factoryInstance.addDefaultMap(6, DictSingleFilterModel);
factoryInstance.addDefaultMap(7, DictMultiFilterModel);
factoryInstance.addDefaultMap(8, DictSingleSearchFilterModel);
factoryInstance.addDefaultMap(9, DictMultiSearchFilterModel);
factoryInstance.addDefaultMap(10, PropetrySingleSearchFilterModel);
factoryInstance.addDefaultMap(11, PropertyMultiSearchFilterModel);
factoryInstance.addDefaultMap(13, TreeMultiFilterModel);
factoryInstance.addDefaultMap(14, KeywordFilterModel);
factoryInstance.addDefaultMap(15, TwoDimensionFilterModel);
factoryInstance.addDefaultMap(16, ThreeDimensionFilterModel);
factoryInstance.addDefaultMap(17, FourDimensionFilter);
// PC端存在空标签的使用情况，而且该数据还得保留下来
factoryInstance.addDefaultMap('Empty', EmptyFilterModel);

export default factoryInstance;
