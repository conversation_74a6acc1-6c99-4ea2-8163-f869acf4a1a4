/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-08-04
 * @Description  : AI-秒圈【规则块】
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import cx from 'classnames';

import RuleRelationTag from '../RuleRelationTag';
import FilterBar from '../FilterBar';

import styles from './index.less';

interface IFilterRuleBlockProps {
  data: {
    filtersData: any[];
    relation: string;
  };
  order: number;
  showOrder: boolean;
}

class FilterRuleBlock extends PureComponent<IFilterRuleBlockProps> {
  static propTypes = {
    data: PropTypes.object.isRequired,
    order: PropTypes.number.isRequired,
    showOrder: PropTypes.bool.isRequired,
  }

  @autobind
  renderFilter(item: any, index: number) {
    return (
      <FilterBar
        key={item.uuid}
        data={item}
        order={index}
      />
    );
  }

  render() {
    const { data, order, showOrder } = this.props;

    const isFirst = order === 0;

    const title = showOrder ? `规则${order + 1}：` : '规则：';

    const ruleTitleCls = cx({
      [styles.ruleTitle]: true,
      [styles.isFirstTitle]: isFirst,
    });

    const filterAreaCls = cx({
      [styles.filters]: true,
      [styles.multiFilter]: _.size(data.filtersData) > 1,
    });

    return (
      <div className={styles.filterRulelock}>
        <div className={styles.header}>
          <RuleRelationTag visible={!isFirst} relationId={data.relation} />
          <div className={ruleTitleCls}>
            {title}
          </div>
        </div>
        <div className={filterAreaCls}>
          {_.map(data.filtersData, this.renderFilter)}
        </div>
      </div>
    );
  }
}

export default FilterRuleBlock;
