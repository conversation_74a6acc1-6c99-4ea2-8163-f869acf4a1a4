.filterRulelock {
  max-width: 800px;
  min-width: 458px;
  min-height: 101px;
  margin-bottom: 13px;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  background-color: #fff;
  box-sizing: border-box;

  .header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 40px;
    background: #f2f5f7;
    border-radius: 3px 3px 0 0;

    .ruleTitle {
      flex: 0 0 auto;
      margin-left: 10px;
      height: 20px;
      font-size: 14px;
      color: #333;
      line-height: 20px;

      &.isFirstTitle {
        margin-left: 14px;
      }
    }
  }

  .filters {
    padding: 20px 0;
    height: fit-content;
    box-sizing: border-box;

    &.multiFilter {
      padding: 15px 0;
    }
  }
}
