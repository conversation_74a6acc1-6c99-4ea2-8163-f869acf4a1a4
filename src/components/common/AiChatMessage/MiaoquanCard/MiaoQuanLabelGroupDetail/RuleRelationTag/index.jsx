/*
 * @Author: ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-17 10:07:30
 * @Last Modified by: chen<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-05-17 11:00:42
 * @description: 客群创建-规则块-左上角关系标签
 */

import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import {
  RULE_TYPE_NAME,
  COMMON001,
  COMMON002,
  COMMON003,
  COMMON004,
  COMMON005,
} from './config';

import styles from './index.less';

function RuleRelationTag(props) {
  const {
    visible,
    relationId,
  } = props;

  if (!visible) {
    return null;
  }

  const realtionCls = cx({
    [styles.ruleTypeImg]: true,
    [styles.COMMON001]: relationId === COMMON001,
    [styles.COMMON002]: relationId === COMMON002,
    [styles.COMMON003]: relationId === COMMON003,
    [styles.COMMON004]: relationId === COMMON004,
    [styles.COMMON005]: relationId === COMMON005,
  });

  return (
    <div className={realtionCls}>
      {RULE_TYPE_NAME[relationId]}
    </div>
  );
}

RuleRelationTag.propTypes = {
  // 关系ID
  relationId: PropTypes.string.isRequired,
  visible: PropTypes.bool.isRequired,
};

export default React.memo(RuleRelationTag);
