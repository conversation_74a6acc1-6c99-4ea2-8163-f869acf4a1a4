/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-08-05
 * @Description  : AI-秒圈【规则块】-【筛选条件】-获取FilterModel对象从而获取valueLabel
 */
import { useEffect, useState } from 'react';

import factoryInstance from '../FilterModels/FilterModelFactory';

function useFilterModelValueLabel(data:any) {
  const [valueLabel, setValueLabel] = useState('');

  useEffect(() => {
    const filterModel = factoryInstance.getFilterModelByData(data);
    const filterValueLabel = filterModel.getValueLabel();
    setValueLabel(filterValueLabel);
  }, [data]);

  return valueLabel;
}

export default useFilterModelValueLabel;
