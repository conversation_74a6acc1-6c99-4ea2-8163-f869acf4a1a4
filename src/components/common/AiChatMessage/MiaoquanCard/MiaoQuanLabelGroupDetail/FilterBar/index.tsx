/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-08-04
 * @Description  : AI-秒圈【规则块】-【筛选条件】
 */
import React from 'react';
import _ from 'lodash';
import cx from 'classnames';
import { Tooltip } from '@ht/sprite-ui';

import FilterRelationTag from '../FilterRelationTag';
import useFilterModelValueLabel from './useFilterModelValueLabel';

import styles from './index.less';

interface IProps {
  data:any;
  order: number;
}

const FilterBar:React.FC<IProps> = (props) => {
  const { data, order } = props;

  const isFirst = order === 0;

  const filterInfoCls = cx({
    [styles.filterInfo]: true,
    [styles.firstFilter]: isFirst,
  });

  const filterValueLabel = useFilterModelValueLabel(data);

  // NOTE: SWB 2025-09-03 新增一种AI秒圈特殊标签场景展示
  // 因为由于AI那边某种场景需要添加一个空白标签占位，相当于【创建客群】的【暂无选择】
  if (data.filterType === 'Empty') {
    return (
      <div className={styles.filterBar}>
        <FilterRelationTag visible={!isFirst} relationId={data.relation} />
        <div className={filterInfoCls}>
          <div className={styles.allEmployee}>全体客户</div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.filterBar}>
      <FilterRelationTag visible={!isFirst} relationId={data.relation} />
      <div className={filterInfoCls}>
        <Tooltip title={data.filterDesc}>
          <div className={styles.filterName}>{data.filterName}</div>
        </Tooltip>
        {filterValueLabel}
      </div>
    </div>
  );
};

export default React.memo(FilterBar);
