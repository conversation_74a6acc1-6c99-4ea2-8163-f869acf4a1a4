.filterBar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  max-width: 800px;
  min-width: 458px;
  min-height: 20px;
  padding: 0 15px;
  margin-bottom: 15px;
  box-sizing: border-box;

  &:last-child {
    margin-bottom: 0;
  }

  .filterInfo {
    flex: 0 0 auto;
    width: calc(100% - 62px - 30px);
    min-height: 20px;
    font-size: 14px;
    color: #333;
    line-height: 20px;
    word-break: break-all;
    box-sizing: border-box;

    &.firstFilter {
      width: calc(100% - 30px);
    }
  }

  .allEmployee {
    float: left;
    height: 20px;
    font-size: 14px;
    color: #108ee9;
    line-height: 20px;
    quotes: "「" "」";
    cursor: pointer;

    &::before {
      content: open-quote;
    }

    &::after {
      content: close-quote;
    }
  }

  .filterName {
    float: left;
    height: 20px;
    font-size: 14px;
    color: #108ee9;
    line-height: 20px;
    quotes: "「" "」";
    cursor: pointer;

    &:hover {
      font-weight: bold;
    }

    &::before {
      content: open-quote;
    }

    &::after {
      content: close-quote;
    }
  }
}
