/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-17 11:09:58
 * @Last Modified by: sunweibin
 * @Last Modified time: 2023-05-20 11:54:49
 * @description: 客群创建-规则块内-关系标签
 */

import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import {
  STDINTERFACEPROPERTY_NAME,
  COMMON001,
  COMMON002,
  COMMON003,
} from './config';

import styles from './index.less';

function FilterRelationTag(props) {
  const { relationId, visible } = props;

  if (!visible) {
    return null;
  }

  const realtionCls = cx({
    [styles.property]: true,
    [styles.COMMON001]: relationId === COMMON001,
    [styles.COMMON002]: relationId === COMMON002,
    [styles.COMMON003]: relationId === COMMON003,
  });

  return (
    <div className={realtionCls}>
      {STDINTERFACEPROPERTY_NAME[relationId]}
    </div>
  );
}

FilterRelationTag.propTypes = {
  visible: PropTypes.bool.isRequired,
  // 关系ID
  relationId: PropTypes.string.isRequired,
};

export default React.memo(FilterRelationTag);
