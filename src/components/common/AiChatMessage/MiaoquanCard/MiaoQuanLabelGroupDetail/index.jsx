/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-07-30
 * @Description  : AI-秒圈【圈客方案】卡片
 * @Wiki         : http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=418055285
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import FilterRuleBlock from './FilterRuleBlock';

import styles from './index.less';

class MiaoQuanLabelGroupDetail extends PureComponent {
  static propTypes = {
    data: PropTypes.array.isRequired,
  }

  @autobind
  renderRuleBlock(item, index, rules) {
    // NOTE: SWB 2025-08-19 只有一个规则块的时候不展示标题中的序号
    const showOrder = _.size(rules) > 1;

    if (item.ruleType === 'FilterRule') {
      return (
        <FilterRuleBlock
          key={item.uuid}
          order={index}
          data={item}
          showOrder={showOrder}
        />
      );
    }

    if (item.ruleType === 'ClientRule') {
      // TODO: SWB 2025-08-04 后期如果需要添加【客户名单】在此处渲染
      return null;
    }


    return null;
  }

  render() {
    const { data } = this.props;

    return (
      <div className={styles.labelGroupDetail}>
        {_.map(data, this.renderRuleBlock)}
      </div>
    );
  }
}

export default MiaoQuanLabelGroupDetail;
