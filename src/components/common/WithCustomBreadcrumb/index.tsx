/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-24
 * @Description：自定义面包屑
 */
import React, { PureComponent } from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';
import hoistStatics from 'hoist-non-react-statics';
import { generateEffect } from '@/utils/dvaHelper';
import type { MenuDataItem } from '@umijs/route-utils';

type WithCustomBreadcrumbProps = {
  setCustomBreadcrumb: (customBreadcrumb: MenuDataItem[]) => void;
};

type WrappedComponentProps = {
  setBreadcrumb: (breadcrumbList: MenuDataItem[]) => void;
}
const WithCustomBreadcrumb = (breadcrumbs: MenuDataItem[]) => (WrappedComponent: React.ComponentType<WrappedComponentProps>) => {
  class C extends PureComponent<WithCustomBreadcrumbProps> {
    componentDidMount() {
      if (!_.isEmpty(breadcrumbs)) {
        this.customBreadcrumbChange(breadcrumbs);
      }
    }

    componentWillUnmount() {
      // 清空面包屑
      this.props.setCustomBreadcrumb([]);
    }

    // 设置面包屑
    customBreadcrumbChange = (breadcrumbList: MenuDataItem[]) => {
      this.props.setCustomBreadcrumb(breadcrumbList);
    }

    render() {
      return (
        <WrappedComponent
          {...this.props}
          setBreadcrumb={this.customBreadcrumbChange}
        />
      );
    }
  }

  const mapDispatchToProps = {
    setCustomBreadcrumb: generateEffect('global/setCustomBreadcrumb'),
  };

  // @ts-expect-error
  return hoistStatics(connect(null, mapDispatchToProps)(C), WrappedComponent);
};

export default WithCustomBreadcrumb;
