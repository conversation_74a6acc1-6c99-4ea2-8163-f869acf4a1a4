/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-11
 * @Description：文件管理-顶部右边按钮
 */
import React from 'react';
import Button from '@/components/common/Button';
import type { IRoleInfo } from '@/services/types/api/login/queryEmpInfo';
import styles from './topRightButton.less';

type TopRightButtonProps = {
  /** 是否为批量操作 */
  batchOperation: boolean;
  /** 相关批量操作按钮是否禁用 */
  batchOperationButDisabled: boolean;
  /** 角色权限信息 */
  roleInfo: IRoleInfo;
  /** 取消 */
  cancelOperation: () => void;
  /** 批量管理 */
  updateBatchOperation: () => void;
  /** 批量编辑 */
  onBatchEdit: () => void;
  /** 命中测试 */
  hitTest: () => void;
  /** 打开类目Modal */
  openCategoryModal: (type: string) => void;
};
const TopRightButton: React.FC<TopRightButtonProps> = ({
  batchOperation,
  batchOperationButDisabled,
  roleInfo,
  cancelOperation,
  updateBatchOperation,
  onBatchEdit,
  hitTest,
  openCategoryModal,
}) => {
  return (
    <div className={styles.topRightButton}>
      {batchOperation ? (
        <div>
          <span className={styles.tipText}>请选择文件</span>
          <Button onClick={cancelOperation}>取消</Button>
          <Button
            type="primary"
            disabled={batchOperationButDisabled}
            onClick={onBatchEdit}
          >
            批量编辑
          </Button>
          <Button
            type="primary"
            disabled={batchOperationButDisabled}
            onClick={() => hitTest()}
          >
            命中测试
          </Button>
          <Button
            type="primary"
            disabled={batchOperationButDisabled}
            onClick={() => openCategoryModal('batchEditCategory')}
          >
            批量编辑类目
          </Button>
        </div>
      ) : (
        // 判断是否有编辑权限，无编辑权限disabled
        <div>
          <Button
            type="primary"
            disabled={!roleInfo?.edit}
            onClick={updateBatchOperation}
          >
            批量管理
          </Button>
          <Button
            type="primary"
            disabled={!roleInfo?.edit}
            onClick={() => openCategoryModal('importData')}
          >
            导入数据
          </Button>
        </div>
      )}
    </div>
  );
};

export default TopRightButton;
