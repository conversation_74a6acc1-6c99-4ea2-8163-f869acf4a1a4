/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-11
 * @Description：文件管理-列表筛选条件信息
 */
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { AutoComplete, Input } from '@ht/sprite-ui';
import { SearchOutlined } from '@ht-icons/sprite-ui-react';
import type { FileCategoryItem } from '@/services/types/api/fileManagement/queryFileCategory';
import type { DataSourcesItem } from '@/services/types/api/fileManagement/queryDataSources';
import FilterCascader from './FilterCascader';
import styles from './filterInfo.less';

type FilterInfoProps = {
  /** 文件类目 */
  fileCategory: FileCategoryItem[];
  /** 数据来源 */
  dataSources: DataSourcesItem[];
  /** 文件管理字典 */
  fileManageDict: any;
  filterInfoChange: (filterFieldName: string, value: any) => void;
};
const FilterInfo: React.FC<FilterInfoProps> = ({
  fileCategory,
  dataSources,
  fileManageDict,
  filterInfoChange,
}) => {
  const [searchFileName, setSearchFileName] = useState<string>('');
  const [categoryLabel, setCategoryLabel] = useState<string>('不限');
  const [dataSourceLabel, setDataSourceLabel] = useState<string>('不限');
  const [validateStatusLabel, setValidateStatusLabel] = useState<string>('不限');
  const [validateStatusOpts, setValidateStatusOpts] = useState<any[]>([{ label:'不限', value:'' }]);
  const [fileStatusLabel, setFileStatusLabel] = useState<string>('不限');
  const  [fileStatusOpts, setFileStatusOpts] = useState<any[]>([{ label:'不限', value:'' }]);

  useEffect(() => {
    if (fileManageDict?.chunkVerifyDict?.length) {
      setValidateStatusOpts([{ label:'不限', value:'' }].concat(fileManageDict?.chunkVerifyDict));
    }
  }, [fileManageDict?.chunkVerifyDict]);

  useEffect(() => {
    if (fileManageDict?.fileStatusDict?.length) {
      setFileStatusOpts([{ label:'不限', value:'' }].concat(fileManageDict?.fileStatusDict));
    }
  }, [fileManageDict?.fileStatusDict]);

  // 处理显示文本
  const handleDisplayText = (selectedOptions: any[]) => {
    let cascaderDisplayText = '';
    _.forEach(selectedOptions, (selectedOption: any[], index: number) => {
      const labels = _.map(selectedOption, (item: { label: string; }) => item.label);
      cascaderDisplayText += labels.join('-');
      if (index !== selectedOptions.length - 1) {
        cascaderDisplayText += '，';
      }
    });
    return cascaderDisplayText;
  };

  // 文件名称change
  const handleSearchFileNameChange = () => {
    filterInfoChange('fileName', searchFileName);
  };

  // FilterCascader change
  const filterCascaderChange = (fieldName: string, value: (string | number)[][], selectedOptions: any) => {
    filterInfoChange(fieldName, value);
    // 处理类目change
    if (fieldName === 'categoryId') {
      if (_.isEmpty(selectedOptions)) {
        setCategoryLabel('不限');
        return;
      }
      setCategoryLabel(handleDisplayText(selectedOptions));
      return;
    }
    // 处理数据来源change
    if (fieldName === 'dataSourceId') {
      if (_.isEmpty(selectedOptions)) {
        setDataSourceLabel('不限');
        return;
      }
      setDataSourceLabel(handleDisplayText(selectedOptions));
    }
    // 处理切片是否校验change
    if (fieldName === 'chunkVerifyStatus') {
      if (_.isEmpty(selectedOptions)) {
        setValidateStatusLabel('不限');
        return;
      }
      setValidateStatusLabel(selectedOptions?.[0]?.label);
    }
    // 处理当前状态change
    if (fieldName === 'fileStatus') {
      if (_.isEmpty(selectedOptions)) {
        setFileStatusLabel('不限');
        return;
      }
      setFileStatusLabel(selectedOptions?.[0]?.label);
    }

  };

  return (
    <div className={styles.filterInfo}>
      <div className={styles.fileName}>
        <AutoComplete
          value={searchFileName}
          placeholder="请输入文件名称"
          onChange={setSearchFileName}
          className={styles.fileNameAutoComplete}
        >
          <Input
            onPressEnter={handleSearchFileNameChange}
            suffix={<SearchOutlined onClick={handleSearchFileNameChange} />}
          />
        </AutoComplete>
      </div>
      <div className={styles.category}>
        <FilterCascader
          multiple={true}
          options={fileCategory}
          fieldName="类目："
          cascaderLabel={categoryLabel}
          onChange={
            (value, selectOptions) => filterCascaderChange('categoryId', value, selectOptions)
          }
        />
      </div>
      <div className={styles.dataSource}>
        <FilterCascader
          multiple={true}
          options={dataSources}
          fieldName="数据来源："
          cascaderLabel={dataSourceLabel}
          onChange={
            (value, selectOptions) => filterCascaderChange('dataSourceId', value, selectOptions)
          }
          cascaderChildrenClassName={styles.cascaderDisplayRender}
          // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
          popupClassName={styles.dataSourceCascaderDropdownClass}
        />
      </div>
      <div className={styles.dataSource}>
        <FilterCascader
          multiple={false}
          options={validateStatusOpts}
          fieldName="切片是否校验："
          cascaderLabel={validateStatusLabel}
          onChange={
            (value, selectOptions) =>
              filterCascaderChange('chunkVerifyStatus', value?.[0] ? [value] : [], selectOptions)
          }
          cascaderChildrenClassName={styles.cascaderDisplayRender}
          // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
          popupClassName={styles.dataSourceCascaderDropdownClass}
        />
      </div>
      <div className={styles.dataSource}>
        <FilterCascader
          multiple={false}
          options={fileStatusOpts}
          fieldName="当前状态："
          cascaderLabel={fileStatusLabel}
          onChange={
            (value, selectOptions) => filterCascaderChange('fileStatus', value?.[0] ? [value] : [], selectOptions)
          }
          cascaderChildrenClassName={styles.cascaderDisplayRender}
          // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
          popupClassName={styles.dataSourceCascaderDropdownClass}
        />
      </div>
    </div>
  );
};

export default FilterInfo;
