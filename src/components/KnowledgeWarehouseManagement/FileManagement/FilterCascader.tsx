/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-16
 * @Description：文件管理-Cascader筛选条件
 */
import React from 'react';
import { Cascader } from '@ht/sprite-ui';
import classNames from 'classnames';
import _ from 'lodash';
import { DownOutlined } from '@ant-design/icons';
import type { DefaultOptionType, CascaderProps } from '@ht/sprite-ui/lib/cascader';
import styles from './filterCascader.less';

type FilterCascaderProps = CascaderProps<DefaultOptionType> & {
  /** 字段名称 */
  fieldName: string;
  /** 默认显示文本，默认显示【不限】 */
  cascaderLabel?: string;
  /** cascader子节点的class */
  cascaderChildrenClassName?: string;
}
const FilterCascader: React.FC<FilterCascaderProps> = (props) => {
  const {
    fieldName,
    cascaderLabel = '不限',
    cascaderChildrenClassName,
    ...cascaderProps
  } = props;

  // Cascader显示渲染样式
  const cascaderDisplayRenderClassNames = classNames(
    styles.cascaderDisplayRender,
    cascaderChildrenClassName,
  );
  return (
    <Cascader {...cascaderProps}>
      <div className={cascaderDisplayRenderClassNames}>
        <div className={styles.cascaderLeft}>
          <div className={styles.fieldName}>{fieldName}</div>
          <div className={styles.cascaderLabel} title={cascaderLabel}>
            {cascaderLabel}
          </div>
        </div>
        <DownOutlined className={styles.downArrow} />
      </div>
    </Cascader>
  );
};

export default FilterCascader;
