/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-11
 * @Description：文件管理-顶部标题信息
 */
import React from 'react';
import _ from 'lodash';
import styles from './topTitleInfo.less';

type TopTitleInfoProps = {
  title: string;
  rightInfo?: React.ReactNode | (() => React.ReactNode);
};
const TopTitleInfo: React.FC<TopTitleInfoProps> = ({ title, rightInfo }) => (
  <div className={styles.topTitleInfo}>
    <div className={styles.leftText}>{title}</div>
    {_.isFunction(rightInfo) ? (
      <div>{rightInfo()}</div>
    ) : (
      <div>{rightInfo}</div>
    )}
  </div>
);

export default TopTitleInfo;
