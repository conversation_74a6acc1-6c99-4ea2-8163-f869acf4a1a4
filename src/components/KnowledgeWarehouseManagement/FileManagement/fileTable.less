.fileTable {
  .fileNameRender {
    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
  }

  .categoryCascader {
    width: 100%;
  }

  .enabledRender {
    width: 100%;
  }

  .statusRender {
    .success::before,
    .failed::before,
    .processing::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #52c41a;
      border-radius: 50%;
      vertical-align: middle;
      margin-right: 8px;
    }
    .failed::before {
      background: #ff4d4f;
    }
    .processing::before {
      background: #faad14;
    }
  }

  .actionRender {
    button {
      padding: 0;
    }
  }
}
