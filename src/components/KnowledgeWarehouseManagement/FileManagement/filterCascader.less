.cascaderDisplayRender {
  height: 32px;
  min-width: 160px;
  max-width: 360px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .cascaderLeft {
    width: calc(100% - 18px);
    display: flex;
    margin-right: 4px;

    .fieldName {
      color: #6c6f76;
      font-size: 14px;
      flex: none;
    }

    .cascaderLabel {
      color: #3f434b;
      font-size: 14px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .downArrow {
    color: #999ba0;
  }

  &:hover {
    background: #f6f6f7;
  }
}
