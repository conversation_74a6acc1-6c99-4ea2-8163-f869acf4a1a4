/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-11
 * @Description：文件管理-文件列表
 */
import React from 'react';
import _ from 'lodash';
import classNames from 'classnames';
import Button from '@/components/common/Button';
import { Cascader, Modal, Select, Space, Table, Tooltip } from '@ht/sprite-ui';
import { ExclamationCircleFilled } from '@ht-icons/sprite-ui-react';
import type { ColumnsType } from '@ht/sprite-ui/lib/table';
import type { FileCategoryItem } from '@/services/types/api/fileManagement/queryFileCategory';
import type {
  DataType,
  FileManagementList,
  FileManagementListItem,
} from '@/services/types/api/fileManagement/queryFileManagementList';
import type { IRoleInfo } from '@/services/types/api/login/queryEmpInfo';
import excel from './images/excel.png';
import word from './images/word.png';
import mdTag from './images/md.png';
import pdfTag from './images/pdf.png';
import htmlTag from './images/html.png';
import { EnabledEnum } from './config';

import styles from './fileTable.less';

type FileTableProps = {
  rowSelection?: any;
  fileCategory: FileCategoryItem[];
  roleInfo: IRoleInfo;
  fileManagementList: FileManagementList;
  loading: boolean;
  handleEdit: (fileId: string) => void;
  handleHitTest: (fileId: string) => void;
  handleDelete: (fileId: string) => void;
  handlePageChange: (pageNum: number, pageSize: number) => void;
  handleFileListUpdate: (fieldName: string, value: string | (string | number)[], id: string) => void;
};
const FileTable: React.FC<FileTableProps> = ({
  rowSelection,
  fileCategory,
  roleInfo,
  fileManagementList,
  loading,
  handleEdit,
  handleHitTest,
  handleDelete,
  handlePageChange,
  handleFileListUpdate,
}) => {

  const imgSrcObj = {
    xls: excel,
    xlsx: excel,
    doc: word,
    docx: word,
    md: mdTag,
    pdf: pdfTag,
    html: htmlTag,
  } as any;

  // pagination对象
  const paginationProps = {
    showSizeChanger: true,
    pageSize: fileManagementList?.page?.pageSize || 20,
    current: fileManagementList?.page?.pageNum || 1,
    total: fileManagementList?.page?.totalCount || 0,
    onChange: handlePageChange,
  };

  // 文件名称columns
  const fileNameColumnsRender = (text: string, record: FileManagementListItem) => {
    const imgSrc = imgSrcObj[record.fileFormat];
    return (
      <Tooltip title={text} className={styles.fileNameRender}>
        <img src={imgSrc} alt="" />
        {text}
      </Tooltip>
    );
  };

  // 类目选择后的展示效果
  const displayRender = (labels: string[]) => labels?.join('-');
  // 类目change事件
  const categoryChange = (value: (string | number)[], record: FileManagementListItem) => {
    handleFileListUpdate('fileCategory', value, record.relationId);
  };
  // 类目columns
  const categoryColumnsRender = (text: DataType[], record: FileManagementListItem) => {
    const values = _.map(text, (item) => item.value);
    // 判断是否有编辑权限，无编辑权限disabled
    return (
      <Cascader
        className={styles.categoryCascader}
        disabled={!roleInfo?.edit || record?.fileStatus?.label === '处理中'}
        value={values}
        options={fileCategory}
        allowClear={false}
        displayRender={displayRender}
        onChange={(value) => categoryChange(value, record)}
      />
    );
  };

  // 是否启用change事件
  const enabledChange = (value: string, record: FileManagementListItem) => {
    handleFileListUpdate('isEnabled', value, record.fileId);
  };
  // 是否启用columns
  const enabledColumnsRender = (text: string, record: FileManagementListItem) => {
    // 判断是否有编辑权限，无编辑权限disabled
    return (
      <Select
        className={styles.enabledRender}
        disabled={!roleInfo?.edit}
        value={text}
        options={EnabledEnum}
        onChange={(value) => enabledChange(value, record)}
      />
    );
  };

  // 当前状态columns
  const statusColumnsRender = (text: string, record: FileManagementListItem) => {
    const successTag = text === '已启用';
    const failedTag = text === '未启用';
    const processing = text === '处理中';
    const stateTextClassName = classNames({
      [styles.success]: successTag,
      [styles.failed]: failedTag,
      [styles.processing]: processing,
    });
    return (
      <div className={styles.statusRender}>
        <span className={stateTextClassName}>{text}</span>
      </div>
    );
  };

  // 删除
  const onDelete = (fileId: string) => {
    Modal.confirm({
      title: '确认要删除吗？',
      icon: <ExclamationCircleFilled />,
      onOk() {
        handleDelete(fileId);
      },
    });
  };
  // 操作columns
  const actionColumnsRender = (text: string, record: FileManagementListItem) => {
    return (
      <Space size="middle" className={styles.actionRender}>
        <Button
          type="link"
          disabled={!roleInfo?.edit || record?.fileStatus?.label === '处理中'}
          onClick={() => handleEdit(record.fileId)}
        >
          编辑
        </Button>
        <Button
          type="link"
          disabled={record?.fileStatus?.label === '处理中'}
          onClick={() => handleHitTest(record.fileId)}
        >
          命中测试
        </Button>
        <Button
          type="link"
          disabled={!roleInfo?.edit || record?.fileStatus?.label === '处理中'}
          onClick={() => onDelete(record.fileId)}
        >
          删除
        </Button>
      </Space>
    );
  };
  const columns: ColumnsType<FileManagementListItem> = [
    {
      title: '文件名称',
      dataIndex: 'fileName',
      width: 300,
      ellipsis: {
        showTitle: false,
      },
      render: fileNameColumnsRender,
    },
    {
      title: '文件格式',
      width: 150,
      dataIndex: 'fileFormat',
    },
    {
      title: '版本',
      width: 120,
      dataIndex: 'version',
    },
    {
      title: '类目',
      width: 264,
      dataIndex: 'categoryInfo',
      render: categoryColumnsRender,
    },
    {
      title: '数据来源',
      width: 140,
      dataIndex: ['dataSources', 'label'],
    },
    {
      title: '更新时间',
      width: 200,
      dataIndex: 'updateTime',
    },
    {
      title: '是否启用',
      width: 100,
      dataIndex: 'enabled',
      render: enabledColumnsRender,
    },
    {
      title: '当前状态',
      width: 140,
      dataIndex: ['fileStatus', 'label'],
      render: statusColumnsRender,
    },
    {
      title: '切片是否校验',
      width: 140,
      dataIndex: ['chunkVerifyStatus', 'label'],
    },
    {
      title: '操作',
      width: 200,
      key: 'action',
      render: actionColumnsRender,
    },
  ];

  return (
    <div className={styles.fileTable}>
      <Table
        rowKey="fileId"
        columns={columns}
        dataSource={fileManagementList.list}
        rowSelection={rowSelection}
        pagination={paginationProps}
        loading={loading}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default FileTable;
