/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-17
 * @Description：文件管理-类目相关操作Modal
 */
import React, { useState } from 'react';
import _ from 'lodash';
import { Cascader, Modal } from '@ht/sprite-ui';
import { ExclamationCircleFilled } from '@ht-icons/sprite-ui-react';
import type { DefaultOptionType } from '@ht/sprite-ui/lib/cascader';
import type { FileCategoryItem } from '@/services/types/api/fileManagement/queryFileCategory';

import styles from './categoryModal.less';

type CategoryModalProps = {
  title: string;
  fileCategory: FileCategoryItem[];
  onOk: (categoryValue: (string | number)[]) => void;
  onCancel: () => void;
};
const CategoryModal: React.FC<CategoryModalProps> = ({
  title,
  fileCategory,
  onOk,
  onCancel,
}) => {
  const [categoryValue, setCategoryValue] = useState([]);

  // 类目选择后的展示效果
  const displayRender = (labels: string[]) => labels?.join('-');
  // 类目change事件
  const categoryChange = (value: any, selectedOptions: DefaultOptionType[]) => {
    setCategoryValue(value);
  };

  // 弹框确定事件
  const handleOk = () => {
    if (_.isEmpty(categoryValue)) {
      Modal.warning({
        title: '请选择类目',
        icon: <ExclamationCircleFilled />,
      });
    } else {
      onOk(categoryValue);
    }
  };
  // 弹框取消事件
  const handleCancel = () => {
    setCategoryValue([]);
    onCancel();
  };
  return (
    <div>
      <Modal
        open={true}
        width={600}
        title={title}
        destroyOnClose={true}
        maskClosable={false}
        onOk={handleOk}
        onCancel={handleCancel}
        bodyStyle={{ height: 278 }}
        className={styles.categoryModal}
      >
        <span>类目：</span>
        <Cascader
          placeholder="请选择"
          options={fileCategory}
          className={styles.categoryCascader}
          displayRender={displayRender}
          onChange={categoryChange}
          // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
          popupClassName={styles.cascaderDropdownClass}
        />
      </Modal>
    </div>
  );
};

export default CategoryModal;
