.filterInfo {
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  box-shadow: inset 0 -1px 0 0 #f2f2f3;
  margin-bottom: 24px;

  .fileName {
    width: 360px;
    margin-right: 32px;

    .fileNameAutoComplete {
      width: 100%;
      :global {
        .@{spriteui-prefix}-select-selection-placeholder {
          display: inline-block;
          z-index: 1;
        }
      }
    }
  }

  .category {
    margin-right: 32px;
  }

  .dataSource {
    margin-right: 32px;
    .cascaderDisplayRender {
      max-width: 244px;
    }
  }
}

.dataSourceCascaderDropdownClass {
  :global {
    .@{spriteui-prefix}-cascader-menu-item {
      min-width: 160px;
      max-width: 244px;

      .@{spriteui-prefix}-cascader-menu-item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
