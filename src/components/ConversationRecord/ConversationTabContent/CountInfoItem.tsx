/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-会话维度列表上方统计信息Item
 */
import React from 'react';
import _ from 'lodash';

import styles from './countInfoItem.less';

type CountInfoItemProps = {
  icon: any;
  label: string;
  value: number;
};
const CountInfoItem: React.FC<CountInfoItemProps> = ({
  icon,
  label,
  value,
}) => {

  return (
    <div className={styles.countInfoItem}>
      <img src={icon} />
      <div className={styles.countInfoItemRight}>
        <div className={styles.countInfoValue}>{value}</div>
        <div className={styles.countInfoLabel}>{label}</div>
      </div>
    </div>
  );
};

export default CountInfoItem;
