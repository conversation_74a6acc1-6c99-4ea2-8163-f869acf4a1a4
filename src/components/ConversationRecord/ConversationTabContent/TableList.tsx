/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-会话维度列表
 */
import React from 'react';
import _ from 'lodash';
import Button from '@/components/common/Button';
import { Space, Table, Tooltip } from '@ht/sprite-ui';
import type { ColumnsType } from '@ht/sprite-ui/lib/table';
import type {
  ConversationList,
  ConversationListItem,
} from '@/services/types/api/conversationRecord/queryConversationList';

import styles from './tableList.less';

type FileTableProps = {
  conversationList: ConversationList;
  loading: boolean;
  handleDetail: (conversationId: string, empId: string) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
};
const TableList: React.FC<FileTableProps> = ({
  conversationList,
  loading,
  handleDetail,
  handleTableChange,
}) => {

  // pagination对象
  const paginationProps = {
    showSizeChanger: true,
    pageSize: conversationList?.page?.pageSize || 20,
    current: conversationList?.page?.pageNum || 1,
    total: conversationList?.page?.totalCount || 0,
  };

  // 会话IDcolumns
  const conversationIdRender = (text: string) => {
    return (
      <Tooltip placement='topLeft' title={text}>
        {text}
      </Tooltip>
    );
  };

  // 会话详情columns
  const actionColumnsRender = (text: string, record: ConversationListItem) => {
    let empId = '';
    if (record.questioner) {
      empId = record?.questioner?.split(' ')?.[0] || record.questioner;
    }
    return (
      <Space size="middle" className={styles.actionRender}>
        <Button
          type="link"
          onClick={() => handleDetail(record.conversationId, empId)}
        >
          查看
        </Button>
      </Space>
    );
  };
  const columns: ColumnsType<ConversationListItem> = [
    {
      title: '会话ID',
      dataIndex: 'conversationId',
      width: 250,
      ellipsis: {
        showTitle: false,
      },
      render: conversationIdRender,
    },
    {
      title: '提问人',
      dataIndex: 'questioner',
      width: 220,
    },
    {
      title: '所属部门/分支',
      width: 380,
      dataIndex: 'department',
    },
    {
      title: '员工类型',
      dataIndex: 'tgType',
      width: 220,
      render: (text) => {
        return text || '--';
      },
    },
    {
      title: '首次提问时间',
      width: 210,
      dataIndex: 'questionTime',
      sorter: true,
      defaultSortOrder: 'descend',
      sortDirections: ['ascend', 'descend', 'ascend'],
    },
    {
      title: '问题数',
      width: 160,
      dataIndex: 'questionNum',
    },
    {
      title: '点赞数',
      width: 160,
      dataIndex: 'likesNum',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
    },
    {
      title: '点踩数',
      width: 160,
      dataIndex: 'dislikesNum',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
    },
    {
      title: '会话详情',
      key: 'action',
      width: 110,
      render: actionColumnsRender,
    },
  ];

  return (
    <div className={styles.tableList}>
      <Table
        rowKey="conversationId"
        columns={columns}
        dataSource={conversationList.list}
        pagination={paginationProps}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default TableList;
