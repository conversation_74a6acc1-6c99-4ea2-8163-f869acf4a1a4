.filterInfo {
  width: 100%;
  box-shadow: inset 0 -1px 0 0 #f2f2f3;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  padding: 12px 0;

  .spaceSty {
    height: 100%;
  }

  .timeRange {
    .datePicker {
      width: 256px;
    }
  }

  .questioner {
    .questionerSelect {
      min-width: 160px;
      max-width: 180px;
      &:hover {
        background: #f6f6f7;
      }

      svg {
        width: 14px;
        height: 14px;
        color: #999ba0;
      }

      :global {
        .@{spriteui-prefix}-select-selector {
          align-items: center;

          &::before {
            content: "提问人：";
            display: inline-block;
          }

          .@{spriteui-prefix}-select-selection-search {
            padding-left: 56px;
          }
        }
      }
    }
  }
}
