/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-问题列表筛选条件信息
 */
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import {
  DatePicker, Space, Select, Row, Col, Input,
} from '@ht/sprite-ui';
import type { FeedbackPersonResult, IQueryFeedbackPerson } from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type { DepartmentInfoItem } from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import Button from '@/components/common/Button';
import DepartmentTreeSelect from '@/components/DataFeedback/DepartmentTreeSelect';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import FilterCascader from '@/components/KnowledgeWarehouseManagement/FileManagement/FilterCascader';
import { AnswerFeedbackEnum, SceneCoverageEnum } from './config';

import styles from './filterInfo.less';
import { conversationRecordPersistenceKey } from '@/pages/conversationRecord/config';
import moment from 'moment';
import { SearchOutlined } from '@ht-icons/sprite-ui-react';

const { RangePicker } = DatePicker;

type FilterInfoProps = {
  /** 提问人 */
  questioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  queryQuestionerLoading: boolean;
  /** 查询提问人(同查询反馈人) */
  queryQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  filterInfoChange: (filterFieldName: string, value: any) => void;
  handleQueryData: () => void;
  handleDownloadData: () => void;
};

const getMatchingValues = (initVal: string[]) => {
  const newArr: string[][] = [];
  if (initVal?.length) {
    initVal.forEach(item => {
      newArr.push([item]);
    });
  }
  return newArr;
};

const getMatchingLabels = (values: string[], data: { label: string, value: string }[]) => {
  if (values?.length) {
    // 1. 过滤出value在values数组中的对象
    const matchingItems = data.filter(item => values?.includes(item.value));

    // 2. 提取label属性
    const labels = matchingItems.map(item => item.label);

    // 3. 用逗号拼接所有标签
    return labels.join(', ');
  }
  return '不限';
};
const FilterInfo: React.FC<FilterInfoProps> = ({
  questioner,
  department,
  dictData,
  queryQuestionerLoading,
  queryQuestioner,
  filterInfoChange,
  handleQueryData,
  handleDownloadData,
}) => {

  const persistenceKey = conversationRecordPersistenceKey.questionKey;
  const storageValue = window.sessionStorage.getItem(persistenceKey);
  const sessionParams = storageValue ? JSON.parse(storageValue) : {};
  console.log('sessionParams', sessionParams);

  // 时间范围
  const defaultTimeRange = sessionParams?.filterParams?.startTime &&
    [moment(sessionParams?.filterParams?.startTime), moment(sessionParams?.filterParams?.endTime)];

  // 搜索关键字
  const initKeyWord = sessionParams?.filterParams?.keyWord;

  // 提问人
  const defaultQuestionerValue = sessionParams?.filterParams?.empId;

  // 所属部门
  const defaultFeedbackDepartmentValue = _.isEmpty(sessionParams?.filterParams?.orgList) ?
    null : sessionParams?.filterParams?.orgList;

  // 答案反馈
  const initAnswerFeedbackLabel = getMatchingLabels(sessionParams?.filterParams?.answerFeedback, AnswerFeedbackEnum);
  const initAnswerFeedbackValue = getMatchingValues(sessionParams?.filterParams?.answerFeedback);
  console.log('答案反馈', initAnswerFeedbackLabel, initAnswerFeedbackValue);

  //  场景覆盖
  const initSceneCoverageLabel = getMatchingLabels(sessionParams?.filterParams?.sceneCoverages, SceneCoverageEnum);
  const initSceneCoverageValue = getMatchingValues(sessionParams?.filterParams?.sceneCoverages);

  // 涉及agent
  let initAgentsLabel = '不限';
  const initAgentsValue = getMatchingValues(sessionParams?.filterParams?.agents);

  //  员工类型
  let initEmpTypeLabel = '不限';
  const initEmpTypeValue = getMatchingValues(sessionParams?.filterParams?.tgType);

  // 搜索关键字
  const [keyword, setKeyword] = useState(initKeyWord);

  // 提问人
  const [questionerValue, setQuestionerValue] = useState<string>(defaultQuestionerValue || '不限');

  // 答案反馈
  const [answerFeedbackLabel, setAnswerFeedbackLabel] = useState<string>(initAnswerFeedbackLabel);

  // 场景覆盖
  const [sceneCoverageLabel, setSceneCoverageLabel] = useState<string>(initSceneCoverageLabel);

  // 涉及agent
  const [agentLabel, setAgentLabel] = useState<string>(initAgentsLabel);
  const [defaultAgentsValue, setDefaultAgentsValue] = useState<any>(initAgentsValue);

  // 员工类型
  const [empTypeLabel, setEmpTypeLabel] = useState<string>(initEmpTypeLabel);
  const [defaultEmpTypeValue, setDefaultEmpTypeValue] = useState<any>(initEmpTypeValue);

  useEffect(() => {
    if (defaultQuestionerValue) {
      questionerSearch(defaultQuestionerValue);
    }
  }, [defaultQuestionerValue]);

  useEffect(() => {
    if (dictData?.agentDict) {
      initAgentsLabel = getMatchingLabels(sessionParams?.filterParams?.agents, dictData?.agentDict);
      setAgentLabel(initAgentsLabel);
      setDefaultAgentsValue(initAgentsValue);
    }
  }, [dictData?.agentDict]);

  useEffect(() => {
    if (dictData?.tgTypeDict) {
      initEmpTypeLabel = getMatchingLabels(sessionParams?.filterParams?.tgType, dictData?.tgTypeDict);
      setEmpTypeLabel(initEmpTypeLabel);
      setDefaultEmpTypeValue(initEmpTypeValue);
    }
  }, [dictData?.tgTypeDict]);

  // 提问人下拉数据
  const questionerOptions = _.map(questioner?.dataList, item => {
    return {
      label: `${item?.userName}(${item?.userId})`,
      value: item?.userId,
    };
  });

  // 提问人搜索
  const questionerSearch = (value: string) => {
    queryQuestioner({ keyword: value, pageSize: 10, pageNum: 1 });
  };

  // 提问人选择框change
  const questionerChange = (value: string) => {
    if (value) {
      filterInfoChange('empId', value);
    } else {
      filterInfoChange('empId', '');
      queryQuestioner({ keyword: '', pageSize: 10, pageNum: 1 });
    }
    setQuestionerValue(value ? value : '不限');
  };

  // 提问人展开下拉菜单的回调
  const questionerVisibleChange = (visible: boolean) => {
    if (visible && questionerValue !== '不限' && _.size(questionerOptions) !== 1) {
      queryQuestioner({ keyword: questionerValue, pageSize: 10, pageNum: 1 });
    }
  };

  // 处理显示文本
  const handleDisplayText = (selectedOptions: any[]) => {
    let cascaderDisplayText = '';
    _.forEach(selectedOptions, (selectedOption: any[], index: number) => {
      const labels = _.map(selectedOption, (item: { label: string; }) => item.label);
      cascaderDisplayText += labels.join('-');
      if (index !== selectedOptions.length - 1) {
        cascaderDisplayText += '，';
      }
    });
    return cascaderDisplayText;
  };

  // FilterCascader change
  const filterCascaderChange = (fieldName: string, value: (string | number)[][], selectedOptions: any, setLabel: any) => {
    filterInfoChange(fieldName, value);
    if (_.isEmpty(selectedOptions)) {
      setLabel('不限');
      return;
    }
    setLabel(handleDisplayText(selectedOptions));
  };

  const handleDepartmentChange = (valueObj: any) => {
    filterInfoChange('orgList', valueObj);
  };

  return (
    <div className={styles.filterInfo}>
      <Space size={32} className={styles.spaceSty} wrap={true}>
        <div className={styles.timeRange}>
          <span>提问时间：</span>
          <RangePicker
            defaultValue={defaultTimeRange || []}
            placeholder={['开始时间', '结束时间']}
            className={styles.datePicker}
            onChange={
              (_, dateStrings: [string, string]) => filterInfoChange('timeRange', dateStrings)
            }
          />
        </div>
        <Row justify='center' className={styles.subSearchInput}>
          <Col>问题搜索：</Col>
          <Col><Input suffix={<SearchOutlined />} value={keyword} placeholder='请输入关键词' onChange={(v) => {
            const val = v.target.value;
            setKeyword(val);
            filterInfoChange('keyWord', val);
          }} /></Col>

        </Row>
        <div className={styles.questioner}>
          <Select
            isRemoteSearch={true}
            showSearch={true}
            bordered={false}
            allowClear={questionerValue !== '不限'}
            value={questionerValue}
            options={questionerOptions}
            defaultActiveFirstOption={false}
            loading={queryQuestionerLoading}
            className={styles.questionerSelect}
            onSearch={questionerSearch}
            onChange={questionerChange}
            onDropdownVisibleChange={questionerVisibleChange}
          />
        </div>
        <div>
          <DepartmentTreeSelect
            defaultValue={defaultFeedbackDepartmentValue}
            treeData={department}
            filterInfoChange={filterInfoChange}
            onDepartmentChange={handleDepartmentChange}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={initAnswerFeedbackValue}
            options={AnswerFeedbackEnum}
            fieldName="答案反馈："
            cascaderLabel={answerFeedbackLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('answerFeedback', value, selectOptions, setAnswerFeedbackLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={initSceneCoverageValue}
            options={SceneCoverageEnum}
            fieldName="场景覆盖："
            cascaderLabel={sceneCoverageLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('sceneCoverages', value, selectOptions, setSceneCoverageLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={defaultAgentsValue}
            options={dictData?.agentDict}
            fieldName="涉及Agent："
            cascaderLabel={agentLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('agents', value, selectOptions, setAgentLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={defaultEmpTypeValue}
            options={dictData?.tgTypeDict}
            fieldName="员工类型："
            cascaderLabel={empTypeLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('tgType', value, selectOptions, setEmpTypeLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
      </Space>
      <Space size={12}>
        <Button type="primary" onClick={handleQueryData}>查询</Button>
        <Button type="primary" onClick={handleDownloadData}>下载</Button>
      </Space>
    </div>
  );
};

export default FilterInfo;
