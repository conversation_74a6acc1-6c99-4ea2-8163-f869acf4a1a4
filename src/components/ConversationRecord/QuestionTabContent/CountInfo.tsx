/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-会话维度列表上方统计信息
 */
import React from 'react';
import _ from 'lodash';
import type { QuestionStaticsResult } from '@/services/types/api/conversationRecord/queryQuestionStatics';
import CountInfoItem from './CountInfoItem';
import conversationNum from './images/conversationNum.png';
import questionNum from './images/questionNum.png';
import questionerNum from './images/questionerNum.png';
import likesNum from './images/likesNum.png';
import dislikesNum from './images/dislikesNum.png';

import styles from './countInfo.less';

type CountInfoProps = {
  // 会话维度的统计信息
  questionStatics: QuestionStaticsResult,
};
const CountInfo: React.FC<CountInfoProps> = ({
  questionStatics,
}) => {
  const countInfoArr = [
    {
      icon: conversationNum,
      label: '会话数',
      value: questionStatics?.conversationNum || 0,
    },
    {
      icon: questionNum,
      label: '问题数',
      value: questionStatics?.questionNum || 0,
    },
    {
      icon: questionNum,
      label: '子问题数',
      value: questionStatics?.subQuestionNum || 0,
    },
    {
      icon: questionerNum,
      label: '提问人(去重)',
      value: questionStatics?.questionerNum || 0,
    },
    {
      icon: likesNum,
      label: '点赞数',
      value: questionStatics?.likesNum || 0,
    },
    {
      icon: dislikesNum,
      label: '点踩数',
      value: questionStatics?.dislikesNum || 0,
    },
  ];

  return (
    <div className={styles.countInfo}>
      {
        _.map(countInfoArr, (item) => {
          return <CountInfoItem { ...item } key={item.label} />;
        })
      }
    </div>
  );
};

export default CountInfo;
