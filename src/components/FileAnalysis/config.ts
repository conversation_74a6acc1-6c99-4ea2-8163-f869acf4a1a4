import type { FileType } from '@/services/types/api/fileManagement/getFileInfoList';

/** 面板名称 */
export const Pannels = {
  /** 文档管理列表 */
  FileListPannel: 'FileListPannel',
  /** 文档上传导入面板 */
  FileUploadPannel: 'FileUpLoadPannel',
  /** 文档解析面板 */
  FileAnalysisPannel: 'FileAnalysisPannel',
  /** 文档分片面板 */
  FileChunkPannel: 'FileChunkPannel',
  /** 文档召回测试面板 */
  FileRecallTestPannel: 'FileRecallTestPannel',
  /** 文档数据处理面板 */
  FileDataProcessPannel: 'FileDataProcessPannel',
} as const;

/** 面板名称TS类型 */
export type PannelsType = typeof Pannels;
export type PannelsName = PannelsType[keyof PannelsType];

/** 文档解析进度状态枚举值 */
export const StatusEnum = {
  /** 未开始 */
  UN_START: '0',
  /** 处理中 */
  PROCESSING: '3',
  /** 处理完成 */
  DONE: '2',
  /** 处理失败 */
  ERROR: '-1',
  /** 不需要解析 */
  NO_NEED: '1',
} as const;

export type StatusEnumType = typeof StatusEnum;
export type StatusCodeType = StatusEnumType[keyof StatusEnumType];

/** 源文档的文件类型枚举 */
export const FileTypeEnum: FileType = {
  /** 未开始 */
  PDF: 'pdf',
  /** 处理中 */
  HTML: 'html',
};

/** 文件流类型 */
export const FileStreamTypes = {
  'pdf': 'application/pdf',
  'html': 'text/html',
};
