/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : MD阅览组件的TS定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileAnalysisResult } from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type { IMarkdownResult } from '@/services/types/api/fileManagement/getAnalysisMarkDown';

export interface IProps {
  /** 当前文件解析后的MarkDown内容 */
  currentMarkDownResult: IMarkdownResult | null;
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 解析结果 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 文档切片是否收起 */
  chunkListCollapsed: boolean;
  /** markdown预览是否收起 */
  markdownPreviewCollapsed: boolean;
  /** 点击收起/展开 */
  onCollapsedToggle: () => void;
}
