/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文档列表组件的TS定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';

export interface IProps {
  /** 当前选则的文档 */
  currentFile: IFileInfo | null;
  /** 文档信息列表 */
  fileInfoList: IFileInfo[];
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 点击收起/展开 */
  onCollapsedToggle: () => void;
  /** 切换文档 */
  onSelectFile: (fileInfo: IFileInfo) => void;
}
