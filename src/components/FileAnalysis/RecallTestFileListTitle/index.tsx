/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 命中测试面板下文件列表的标题
 */
import type { IProps } from './data';

import React from 'react';
// import { OutdentOutlined } from '@ht-icons/sprite-ui-react';

import styles from './styles.less';

const RecallTestFileListTitle = (props: IProps) => {
  const { title } = props;

  return (
    <div className={styles.pannelTitle}>
      {/* <div className={styles.icon} onClick={props.onCollapsedToggle}>
        <OutdentOutlined />
      </div> */}
      <div className={styles.text}>{title}</div>
    </div>
  );
};

export default RecallTestFileListTitle;
