/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文档分片项
 */
import type { IProps, IState } from './data';

import React from 'react';
import _ from 'lodash';
import Icon, {
  EditOutlined,
  CloseCircleOutlined,
  ExclamationCircleFilled,
  CheckCircleFilled,
} from '@ht-icons/sprite-ui-react';
import { message, Tooltip, Input } from '@ht/sprite-ui';

import If from '@/components/common/If';

import { ReactComponent as DownAdd } from '@/assets/DownAddNew.svg';
import { ReactComponent as UpAdd } from '@/assets/UpAddNew.svg';
import { ReactComponent as Delete } from '@/assets/Delete.svg';
import { ReactComponent as Save } from '@/assets/Save.svg';

import styles from './styles.less';

class FileChunkItem extends React.PureComponent<IProps, IState>{

  constructor(props: IProps) {
    super(props);

    const { chunkInfo } = props;

    this.state = {
      chunkContent: chunkInfo?.chunkContent || '',
      chunkSummary: chunkInfo?.chunkSummary || '',
    };
  }

  handleEditClick = () => {
    const { chunkInfo } = this.props;

    this.props.onEditChunk(chunkInfo);
  }

  handleDeleteClick = () => {
    const { chunkInfo } = this.props;

    this.props.onDeleteChunk(chunkInfo);
  }

  handleCloseEditClick = () => {
    const { chunkInfo } = this.props;

    this.setState({
      chunkContent: chunkInfo?.chunkContent || '',
    });

    this.props.onCloseEdit(chunkInfo);
  }

  handleSaveClick = () => {
    const { chunkInfo } = this.props;
    const { chunkContent, chunkSummary } = this.state;

    // 非【文章总结】的切片，切片内容不能为空
    if (!chunkInfo.conclusion && _.isEmpty(chunkContent)) {
      message.error('分片内容不能为空！');
      return;
    }

    this.props.onSaveChunk({
      ...chunkInfo,
      chunkContent,
      chunkSummary,
    });
  }

  handleUpAddClick = () => {
    const { chunkInfo } = this.props;

    this.props.onUpAddChunk(chunkInfo);
  }

  handleDownAddClick = () => {
    const { chunkInfo } = this.props;

    this.props.onDownAddChunk(chunkInfo);
  }

  handleChunkContentChange: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    const chunkContent = e.target.value;
    this.setState({
      chunkContent,
    });
  }

  handleChunkSummaryChange: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    const chunkSummary = e.target.value || '';
    this.setState({
      chunkSummary,
    });
  }

  renderExtra = () => {
    const { chunkInfo } = this.props;

    if (chunkInfo?.isEditing) {
      return (
        <React.Fragment>
          <Tooltip title="取消">
            <CloseCircleOutlined onClick={this.handleCloseEditClick} />
          </Tooltip>
          <Tooltip title="保存">
            <Icon component={Save} onClick={this.handleSaveClick} />
          </Tooltip>
        </React.Fragment>
      );
    }

    // 如果是【文章总结】则只有【编辑】按钮
    if (chunkInfo?.conclusion) {
      return (
        <React.Fragment>
          <Tooltip title="编辑">
            <EditOutlined onClick={this.handleEditClick} />
          </Tooltip>
        </React.Fragment>
      );
    }

    return (
      <React.Fragment>
        <Tooltip title="删除">
          <Icon component={Delete} onClick={this.handleDeleteClick} />
        </Tooltip>
        <Tooltip title="编辑">
          <EditOutlined onClick={this.handleEditClick} />
        </Tooltip>
        <Tooltip title="在本段前新增分片">
          <Icon component={UpAdd} onClick={this.handleUpAddClick} />
        </Tooltip>
        <Tooltip title="在本段后新增分片">
          <Icon component={DownAdd} onClick={this.handleDownAddClick} />
        </Tooltip>
      </React.Fragment>
    );
  }

  renderChunkContent = () => {
    const { chunkInfo } = this.props;
    const { chunkContent, chunkSummary } = this.state;

    // NOTE: SWB 2025-09-03 新需求切片内容分为【切片摘要】【切片内容】，此处需要分开展示
    if (!chunkInfo.isEditing) {
      return (
        <div className={styles.content}>
          <div className={styles.chunkSummary}>{chunkSummary}</div>
          <div className={styles.chunkContent}>{chunkContent}</div>
        </div>
      );
    }

    return (
      <div className={styles.contentTextArea}>
        <Input.TextArea
          placeholder='请输入切片摘要'
          className={styles.chunkSummaryTextArea}
          autoSize={true}
          value={chunkSummary}
          onChange={this.handleChunkSummaryChange}
        />
        <Input.TextArea
          placeholder='请输入内容'
          className={styles.chunkContentTextArea}
          autoSize={true}
          value={chunkContent}
          onChange={this.handleChunkContentChange}
        />
      </div>
    );
  }

  renderTitle = () => {
    const {
      totalCount,
      chunkInfo,
    } = this.props;

    const chunkNoTitleText = `分片${chunkInfo.no || ''}/共${totalCount}`;

    // 非【文章总结】并且当前切片处于【编辑状态】
    if (!chunkInfo.conclusion && chunkInfo?.isEditing) {
      return (
        <div className={styles.titleArea}>
          <div className={styles.titleText}>{chunkNoTitleText}</div>
        </div>
      );
    }

    // 非【文章总结】并且【已校验】
    if (!chunkInfo.conclusion && chunkInfo?.checkoutFlag) {
      return (
        <div className={styles.titleArea}>
          <div className={styles.titleText}>{chunkNoTitleText}</div>
          <CheckCircleFilled className={styles.checkoutIcon} />
          <div className={styles.checkoutText}>已校验</div>
        </div>
      );
    }

    // 非【文章总结】并且【未校验】
    if (!chunkInfo.conclusion && !chunkInfo?.checkoutFlag) {
      return (
        <div className={styles.titleArea}>
          <div className={styles.titleText}>{chunkNoTitleText}</div>
          <ExclamationCircleFilled className={styles.noCheckoutIcon} />
          <div className={styles.checkoutText}>未校验</div>
        </div>
      );
    }

    return (<div className={styles.conclusionTitle}>文章总结</div>);
  }

  render() {
    const {
      chunkInfo,
      currentFile,
    } = this.props;

    const elementId = `${currentFile?.fileId || ''}-${chunkInfo.chunkId}`;

    return (
      <div id={elementId} className={styles.fileChunkItem}>
        <div className={styles.hedaer}>
          {this.renderTitle()}
          <div className={styles.extra}>
            {this.renderExtra()}
          </div>
        </div>
        {this.renderChunkContent()}
      </div>
    );
  }
}

export default FileChunkItem;
