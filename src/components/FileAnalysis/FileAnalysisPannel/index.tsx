/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件解析面板页面
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { withPollingProps } from '@/components/FileAnalysis/withAnalyisisPolling';
import type { IProps, IState } from './data.d';

import React, { PureComponent } from 'react';
import { connect } from '@oula/oula';
import { Button, Steps } from '@ht/sprite-ui';
import _ from 'lodash';

import { generateEffect } from '@/utils/dvaHelper';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import FileList from '@/components/FileAnalysis/FileList';
import OriginFilePreviewPannel from '@/components/FileAnalysis/OriginFilePreviewPannel';
import AnalysisMarkDownPreviewPannel from '@/components/FileAnalysis/AnalysisMarkDownPreviewPannel';
import withAnalyisisPolling from '@/components/FileAnalysis/withAnalyisisPolling';
import { CurrentPannelContext } from '@/components/FileAnalysis/PannelContext';
import { Pannels, StatusEnum } from '@/components/FileAnalysis/config';

import styles from './styles.less';

const CurrentPannel = { pannelName: Pannels.FileAnalysisPannel };

const mapStateToProps = (state: any) => ({
  /** 文档列表 */
  fileInfoList: state.fileManagement.fileInfoList,
  /** 文档列表的解析进度结果 */
  fileAnalysisResult: state.fileManagement.fileAnalysisResult,
});

const mapDispatchToProps = {
  /** 获取文档解析等页面需要的文档信息 */
  getFileInfoList: generateEffect('fileManagement/getFileInfoList'),
  /** 获取文件解析后的MarkDown文件内容 */
  getAnalysisMarkDown: generateEffect('fileManagement/getAnalysisMarkDown'),
  /** 保存MarkDown内容 */
  saveAnalysisMarkDown: generateEffect('fileManagement/saveAnalysisMarkDown'),
  /** 获取源文档预览地址 */
  getPreviewUrl: generateEffect('fileManagement/getPreviewUrl'),
};

type PannelPropsType = IProps & withPollingProps;

class FileAnalysisPannel extends PureComponent<PannelPropsType, IState> {
  constructor(props: PannelPropsType) {
    super(props);

    this.state = {
      currentFile: null,
      currentMarkDownResult: null,
      currentOriginPreviewUrl: null,
      fileListCollapsed: false,
      originFilePreviewCollapsed: false,
      markdownPreviewCollapsed: false,
    };
  }

  componentDidMount() {
    // 初始化页面查询
    this.initPageData();
  }

  componentDidUpdate(prevProps: Readonly<PannelPropsType>): void {
    const { currentPannel: prevCurrentPannel, fileAnalysisResult: preResult } = prevProps;
    const { currentPannel: nextCurrentPannel, fileAnalysisResult: nextResult } = this.props;

    if (prevCurrentPannel !== nextCurrentPannel && _.last(nextCurrentPannel) === Pannels.FileAnalysisPannel) {
      // 如果当前展示在页面能看到的面板是【文件解析】则需要开启轮询
      const { fileIdList } = this.props;
      this.props.startPolling(fileIdList);
    }

    // NOTE: SWB 2025-05-27 如果文档解析进度变化了则需要进行查询当前解析的MD预览文件
    if (preResult !== nextResult) {
      this.refreshCurrentMarkDown();
    }
  }

  refreshCurrentMarkDown = () => {
    const { fileAnalysisResult } = this.props;
    const { currentMarkDownResult, currentFile } = this.state;

    // NOTE: SWB 2025-05-27 找到当前文件的解析结果
    const currentAnalysisResult = _.find(fileAnalysisResult, item => item.fileId === currentFile?.fileId);

    if (_.isEmpty(currentMarkDownResult?.url) && currentAnalysisResult?.analysisResult === StatusEnum.DONE) {
      this.getAnalysisMarkdownResult();
    }
  }

  initPageData = () => {
    const { fileIdList } = this.props;

    // 1. 初始化请求解析的文档信息
    this.props.getFileInfoList({
      fileIdList,
    }).then(this.initCurrentFile);

    // 2. 开启解析结果轮询
    this.props.startPolling(fileIdList);
  };

  initCurrentFile = () => {
    const { fileInfoList } = this.props;
    this.setState({
      currentFile: fileInfoList[0],
    }, this.doActionAfterInitCurrentFile);
  };

  handleFileCollapsedToggle = () => {
    const { fileListCollapsed } = this.state;

    this.setState({
      fileListCollapsed: !fileListCollapsed,
    });
  };

  handleOriginFilePreviewCollapsedToggle = () => {
    const { originFilePreviewCollapsed } = this.state;

    this.setState({
      originFilePreviewCollapsed: !originFilePreviewCollapsed,
    });
  };

  handleMarkdownPreviewCollapsedToggle = () => {
    const { markdownPreviewCollapsed } = this.state;

    this.setState({
      markdownPreviewCollapsed: !markdownPreviewCollapsed,
    });
  };

  handleFileSelect = (fileInfo: IFileInfo) => {
    this.setState({
      currentFile: fileInfo,
      // NOTE: SWB 2025-04-16 切换文档先将解析结果MarkDown清空掉
      currentMarkDownResult: null,
      // NOTE: SWB 2025-04-23 切换文档清空原始文档预览地址
      currentOriginPreviewUrl: null,
    }, this.doActionAfterInitCurrentFile);
  }

  doActionAfterInitCurrentFile = () => {
    // 1. 获取源文档预览地址
    this.getCurrentOriginPreviewUrl();
    // 2. 获取MarkDown阅览信息
    this.getAnalysisMarkdownResult();
  }

  getCurrentOriginPreviewUrl = () => {
    const { currentFile } = this.state;

    this.props.getPreviewUrl({
      fileId: currentFile?.fileId as string,
    }).then((previewUrls) => {
      this.setState({
        currentOriginPreviewUrl: previewUrls?.previewUrlForEip,
      });
    });
  }

  getAnalysisMarkdownResult = () => {
    const { currentFile } = this.state;

    this.props.getAnalysisMarkDown({
      fileId: currentFile?.fileId as string,
    }).then((result) => {
      this.setState({
        currentMarkDownResult: result,
      });
    });
  }

  handlePrevStep = () => {
    this.props.clearPolling();
    this.props.onPrevStep();
  }

  handleNextStep = () => {
    this.props.stopPolling();
    this.props.onNextStep();
  }

  render() {
    const { fileInfoList, fileAnalysisResult } = this.props;

    const {
      currentFile,
      fileListCollapsed,
      originFilePreviewCollapsed,
      markdownPreviewCollapsed,
      currentMarkDownResult,
      currentOriginPreviewUrl,
    } = this.state;

    return (
      <CurrentPannelContext.Provider value={CurrentPannel}>
        <div className={styles.fileAnalysisPannel}>
          <TopTitleInfo title="文件导入" rightInfo={null} />
          <div className={styles.processArea}>
            <Steps current={1}>
              <Steps.Step title="文件上传及接入" />
              <Steps.Step title="文件解析" />
              <Steps.Step title="文件切片" />
              {/* <Steps.Step title="数据处理" /> */}
              <Steps.Step title="命中测试" />
            </Steps>
          </div>
          <div className={styles.threePannels}>
            <FileList
              currentFile={currentFile}
              fileInfoList={fileInfoList}
              fileListCollapsed={fileListCollapsed}
              onCollapsedToggle={this.handleFileCollapsedToggle}
              onSelectFile={this.handleFileSelect}
            />
            <OriginFilePreviewPannel
              currentFile={currentFile}
              currentOriginPreviewUrl={currentOriginPreviewUrl}
              fileListCollapsed={fileListCollapsed}
              originFilePreviewCollapsed={originFilePreviewCollapsed}
              markdownPreviewCollapsed={markdownPreviewCollapsed}
              onCollapsedToggle={this.handleOriginFilePreviewCollapsedToggle}
            />
            <AnalysisMarkDownPreviewPannel
              key={currentFile?.fileId}
              currentMarkDownResult={currentMarkDownResult}
              currentFile={currentFile}
              fileAnalysisResult={fileAnalysisResult}
              fileListCollapsed={fileListCollapsed}
              originFilePreviewCollapsed={originFilePreviewCollapsed}
              markdownPreviewCollapsed={markdownPreviewCollapsed}
              onCollapsedToggle={this.handleMarkdownPreviewCollapsedToggle}
              saveAnalysisMarkDown={this.props.saveAnalysisMarkDown}
            />
          </div>
          <div className={styles.btnArea}>
            <Button onClick={this.handlePrevStep}>上一步</Button>
            <Button type="primary" onClick={this.handleNextStep}>下一步</Button>
          </div>
        </div>
      </CurrentPannelContext.Provider>
    );
  }
}

// @ts-expect-error
export default connect(mapStateToProps, mapDispatchToProps)(withAnalyisisPolling(FileAnalysisPannel));
