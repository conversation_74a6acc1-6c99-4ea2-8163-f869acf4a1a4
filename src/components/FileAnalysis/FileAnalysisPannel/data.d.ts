/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件解析面板页面的TS类型定义
 */
import type { withAnalyisisPollingProps } from '@/components/FileAnalysis/withAnalyisisPolling';
import type {
  IFileInfo,
  IGetFileInfoList,
} from '@/services/types/api/fileManagement/getFileInfoList';
import type {
  IFileAnalysisResult,
} from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type {
  IGetAnalysisiMarkdownResult,
  IMarkdownResult,
} from '@/services/types/api/fileManagement/getAnalysisMarkDown';
import type {
  ICloudPreviewURL,
  IGetPreviewUrl,
} from '@/services/types/api/fileManagement/getPreviewUrl';
import type {
  ISaveAnalysisMarkDown,
} from '@/services/types/api/fileManagement/saveAnalysisMarkDown';
import type { PannelsName } from '@/components/FileAnalysis/config';

export interface IProps {
  /** 当前面板 */
  currentPannel: PannelsName[];
  /** 请求数据用的文件ID列表 */
  fileIdList: string[],
  /** 文档列表 */
  fileInfoList: IFileInfo[];
  /** 文档列表的解析进度结果 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 获取文档解析等页面需要的文档信息 */
  getFileInfoList: (query: IGetFileInfoList['Query']) => Promise<unknown>;
  /** 获取文件解析后的MarkDown文件内容 */
  getAnalysisMarkDown: (query: IGetAnalysisiMarkdownResult['Query']) => Promise<IMarkdownResult>;
  /** 保存MarkDown内容 */
  saveAnalysisMarkDown: (query: ISaveAnalysisMarkDown['Query']) => Promise<boolean>;
  /** 获取源文档预览地址 */
  getPreviewUrl: (query: IGetPreviewUrl['Query']) => Promise<ICloudPreviewURL>;
  /** 上一步回调 */
  onPrevStep: () => void;
  /** 下一步回调 */
  onNextStep: () => void;
}

export interface IState {
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 当前文件解析后的MarkDown内容 */
  currentMarkDownResult: IMarkdownResult | null;
  /** 当前文档原始文档的预览地址 */
  currentOriginPreviewUrl: string | null;
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 原始文档预览是否收起 */
  originFilePreviewCollapsed: boolean;
  /** markdown预览是否收起 */
  markdownPreviewCollapsed: boolean;
}
