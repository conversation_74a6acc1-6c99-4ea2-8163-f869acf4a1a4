/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-05-27
 * @Description  : 获取文档上传Table的scroll属性
 */

import { useEffect, useState } from 'react';

import dom from '@/utils/dom';

interface IScrollProps {
  y: number;
}

/**
 * 获取文档上传Table的scroll
 * @returns Table组件的scroll配置
 */
function useTableScroll(): IScrollProps {
  const [scrollProps, setScrollProps] = useState({ y: 250 });

  useEffect(() => {
    const calcScrollY = () => {
      // 视口高度
      const viewHeight = dom.getViewPortHeight();
      // 视口高度-面包屑高度-步骤条高度-上传区域高度- 提示栏高度 -表格头 - 按钮高度
      const scrollY = viewHeight - 64 - 88 - 214 - 64 - 40 - 72;
      setScrollProps({
        y: scrollY,
      });
    };

    calcScrollY();

    window.addEventListener('resize', calcScrollY);

    return () => {
      window.removeEventListener('resize', calcScrollY);
    };
  }, []);

  return scrollProps;
}

export default useTableScroll;
