// 根据字节数获取文件大小，并转换成 KB、MB、GB、TB 等形式
export const formatFileSize = (bytes: number) => {
  const units = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  let size = bytes;
  let unitIndex = 0;
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  // 保留两位小数，四舍五入
  size = Math.round(size * 100) / 100;
  return `${size} ${units[unitIndex]}`;
};
// 获取文件格式
export const getFileFormat = (fileName: string) => {
  return fileName.split('.').pop()?.toUpperCase();
};
