/*
 * <AUTHOR> sun<PERSON>bin<K0100008>
 * @Date         : 2025-04-15
 * @Description  : 根据其他项的展开和收起宽度变化
 */

import { useEffect, useState } from 'react';

interface PreviewStyle {
  width: string;
}

/**
 * 获取【MD阅览】的宽度
 * @param fileListCollapsed 文件列表收起
 * @param originFilePreviewCollapsed 源文档预览收起
 * @returns MD阅览的宽度
 */
function useMarkdownStyle(fileListCollapsed: boolean, originFilePreviewCollapsed: boolean): PreviewStyle {
  const [widthStyle, setWidthStyle] = useState({ width: '41.667%' });

  useEffect(() => {
    // 1. 【文档列表】【源文档预览】都展开
    if (!fileListCollapsed && !originFilePreviewCollapsed) {
      setWidthStyle({
        width: '41.667%',
      });
    }

    // 2. 如果【文档列表】【源文档预览】都收起
    if (fileListCollapsed && originFilePreviewCollapsed) {
      setWidthStyle({
        width: 'calc(100% - 66px - 66px)',
      });
    }

    // 3. 只有【文档列表】收起
    if (fileListCollapsed && !originFilePreviewCollapsed) {
      setWidthStyle({
        width: 'calc((100% - 66px) / 2)',
      });
    }

    // 4. 只有【源文档预览】收起
    if (originFilePreviewCollapsed && !fileListCollapsed) {
      setWidthStyle({
        width: 'calc(100% - 16.667% - 66px)',
      });
    }
  }, [fileListCollapsed, originFilePreviewCollapsed]);

  return widthStyle;
}

export default useMarkdownStyle;
