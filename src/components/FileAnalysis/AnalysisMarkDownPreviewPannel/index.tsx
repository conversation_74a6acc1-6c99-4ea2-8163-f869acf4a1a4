/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件解析页面的MD阅览
 */
import type { IMarkDownEditorView } from '@/components/FileAnalysis/FileUrlMdEditor/data';
import type { IProps } from './data';

import React, { useState, useRef, useCallback } from 'react';
import _ from 'lodash';
import { v4 as uuid } from 'uuid';
import { EditOutlined, SaveOutlined  } from '@ht-icons/sprite-ui-react';

import PannelTitle from '@/components/FileAnalysis/PannelTitle';
import CollapsedPannelTitle from '@/components/FileAnalysis/CollapsedPannelTitle';
import AnalysisMarkdownFilePreview from '@/components/FileAnalysis/AnalysisMarkdownFilePreview';

import useMarkdownStyle from './useMarkdownStyle';

import styles from './styles.less';

const InitialMdView: IMarkDownEditorView = {
  uuid: uuid(),
  view: { menu: false, md: false, html: true },
};

const AnalysisMarkDownPreviewPannel = (props: IProps) => {
  const {
    currentFile,
    currentMarkDownResult,
    fileAnalysisResult,
    markdownPreviewCollapsed,
    fileListCollapsed,
    originFilePreviewCollapsed,
  } = props;

  const [mdEditorView, setMdEditorView] = useState(InitialMdView);
  const mdContentRef = useRef('');

  const handleMarkDownContentChange = useCallback((text: string) => {
    mdContentRef.current = text;
  }, []);

  const handleMarkDownSave = useCallback(() => {
    props.saveAnalysisMarkDown({
      fileId: currentFile?.fileId || '',
      content:  mdContentRef.current || '',
    }).then((result) => {
      if (result) {
        setMdEditorView({
          uuid: uuid(),
          view: { menu: false, md: false, html: true },
        });
      }
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentFile]);

  const handleMarkDownEdit = () => {
    setMdEditorView({
      uuid: uuid(),
      view: { menu: false, md: true, html: false },
    });
  };

  // NOTE: SWB 2025-04-15 【文档列表】【源文档预览】【MD预览】三个都可各自收起
  // 因此当【文档列表】【源文档预览】各自收起时，文档列表的
  const markdownPreviewStyle = useMarkdownStyle(fileListCollapsed, originFilePreviewCollapsed);

  if (markdownPreviewCollapsed) {
    return (
      <div className={styles.collapsedMarkdownPreview}>
        <div className={styles.content}>
          <CollapsedPannelTitle
            title="MD阅览"
            onCollapsedToggle={props.onCollapsedToggle}
          />
        </div>
      </div>
    );
  }

  // NOTE: SWB 2025-04-16 找到当前文件的解析结果
  const currentAnalysisResult = _.find(fileAnalysisResult, item => item.fileId === currentFile?.fileId);
  // 根据MD不同的View展示不同的图标
  let rightContent = (<div className={styles.mdEditorIcon} onClick={handleMarkDownSave}><SaveOutlined /></div>);
  if (mdEditorView?.view?.html) {
    rightContent = (<div className={styles.mdEditorIcon} onClick={handleMarkDownEdit}><EditOutlined /></div>);
  }

  return (
    <div className={styles.markdownPreview} style={markdownPreviewStyle}>
      <div className={styles.content}>
        <PannelTitle
          onCollapsedToggle={props.onCollapsedToggle}
          title="MD阅览"
          rightContent={rightContent}
        />
        <AnalysisMarkdownFilePreview
          mdEditorView={mdEditorView}
          currentAnalysisResult={currentAnalysisResult?.analysisResult}
          currentMarkDownResult={currentMarkDownResult}
          onChange={handleMarkDownContentChange}
        />
      </div>
    </div>
  );
};

export default AnalysisMarkDownPreviewPannel;
