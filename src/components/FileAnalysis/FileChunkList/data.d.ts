/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文档切片项的TS类型定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileChunkInfo } from '@/services/types/api/fileManagement/getMarkDownChunks';
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';

export interface IProps {
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 文档切片总数 */
  chunkTotalCount: number;
  /** 文档切片信息 */
  chunkList: ChunkInfoItem[];
  /** 删除文档分片 */
  onDeleteChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 点击进入分片编辑状态 */
  onEditChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 点击关闭分片编辑状态 */
  onCloseEdit: (chunkInfo: ChunkInfoItem) => void;
  /** 在当前分片上面新增一个分片 */
  onUpAddChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 在当前分片下面面新增一个分片 */
  onDownAddChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 保存当前文档分片 */
  onSaveChunk: (chunkInfo: ChunkInfoItem) => void;
}
