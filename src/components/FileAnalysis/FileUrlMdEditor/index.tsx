/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 针对MarkDown后端提供url后，前端通过文件流的方式获取文本内容
 */

import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import MdEditor from '@msp/markdown-editor';

import useMarkDownContent from './useMarkDownContent';

import styles from './styles.less';

const FileUrlMdEditor = (props: IProps) => {
  const { mdEditorView, fileUrl } = props;

  const [content, setContent] = useMarkDownContent(fileUrl, props.onChange);

  const handleChange = (text: string) => {
    setContent(text);
    if (props.onChange) {
      props.onChange(text);
    }
  };

  return (
    <div className={styles.markdownEditor}>
      {/* @ts-expect-error */}
      <MdEditor
        key={mdEditorView.uuid}
        view={mdEditorView.view}
        value={content}
        onChange={(editor) => handleChange(editor.text)}
      />
    </div>
  );
};

export default FileUrlMdEditor;
