/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 相似度阈值模块组件
 */
import type { IProps } from './data.d';

import React from 'react';
import _ from 'lodash';
import { InputNumber, Slider } from '@ht/sprite-ui';
import { QuestionCircleOutlined } from '@ht-icons/sprite-ui-react';

import styles from './styles.less';

const ThresholdValueBlock = (props: IProps) => {
  const { thresholdValue } = props;

  return (
    <div className={styles.thresholdValueBlock}>
      <div className={styles.header}>
        <div className={styles.title}>
          <div className={styles.text}>相似度阈值</div>
          <div className={styles.icon}><QuestionCircleOutlined /></div>
        </div>
        <InputNumber
          value={thresholdValue}
          max={1}
          min={0}
          step={0.01}
          onChange={props.onChange}
        />
      </div>
      <div className={styles.sliderArea}>
        <Slider
          value={thresholdValue as number}
          max={1}
          min={0}
          step={0.01}
          onChange={props.onChange}
        />
      </div>
    </div>
  );
};

export default ThresholdValueBlock;
