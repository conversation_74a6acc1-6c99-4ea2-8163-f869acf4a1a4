.thresholdValueBlock {
  margin-bottom: 16px;
  width: 264px;
  height: 126px;
  background: #fff;
  border-radius: 2px;
  border: 1px solid #dddee0;
  box-sizing: border-box;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    width: 100%;
    height: 54px;
    box-sizing: border-box;

    .title {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .text {
        flex: 0 0 auto;
        height: 24px;
        font-size: 16px;
        font-weight: bold;
        color: #3f434b;
        line-height: 24px;
      }

      .icon {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        width: 14px;
        height: 14px;
        color: #6c6f76;
      }
    }
  }

  .sliderArea {
    padding: 0 16px;
    box-sizing: border-box;
  }
}
