.fileInfoItem {
  width: 100%;
  height: 42px;
  padding: 0 8px;
  box-sizing: border-box;

  .fileInfo {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 42px;
    padding: 0 8px 0 10px;
    background: #fff;
    border-radius: 2px;
    box-sizing: border-box;
    cursor: pointer;

    &:hover,
    &.selected {
      background: #f6f6f7;
    }

    &.collapsed {
      justify-content: center;

      .icon {
        margin-right: 0;
      }
    }

    .icon {
      flex: 0 0 auto;
      margin-right: 10px;
      width: 14px;
      height: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .name {
      flex: 1 1 auto;
      height: 22px;
      font-size: 14px;
      color: #3f434b;
      line-height: 22px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .tag {
      flex: 0 0 auto;
      margin-left: 8px;
      width: 52px;
      height: 22px;
      background: #f0f8ff;
      font-size: 12px;
      border-radius: 2px;
      border: 1px solid #b3d5ff;
      color: #006bff;
      line-height: 20px;
      text-align: center;
    }
  }
}
