/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-15
 * @Description  : 文档列表项
 */
import type { IProps } from './data.d';

import React from 'react';
import cx from 'classnames';
import _ from 'lodash';
import { connect } from '@oula/oula';
import { Tooltip } from '@ht/sprite-ui';
import { FileTextOutlined } from '@ht-icons/sprite-ui-react';

import If from '@/components/common/If';
import { StatusEnum } from '@/components/FileAnalysis/config';

import useStatusAdapter from './useStatusAdapter';

import styles from './styles.less';

const FileInfoItem = (props: IProps) => {
  const {
    fileInfo,
    fileAnalysisResult,
    fileChunkProcessResult,
    selectable,
    collapsed,
  } = props;

  const statusCode = useStatusAdapter(fileInfo.fileId, fileAnalysisResult, fileChunkProcessResult);

  const isSelected = selectable ? props.selected : false;
  const handleSelect = selectable ? () => props.onSelect(fileInfo) : _.noop;

  const fileInfoCls = cx({
    [styles.fileInfo]: true,
    [styles.selected]: isSelected,
    [styles.collapsed]: collapsed,
  });

  const isProcessing = statusCode === StatusEnum.PROCESSING || false;

  // 文档列表折叠时只需要展示文件图标Icon
  if (collapsed) {
    return (
      <div className={styles.fileInfoItem}>
        <div className={fileInfoCls} onClick={handleSelect}>
          <Tooltip placement="right" title={fileInfo?.fileName}>
            <div className={styles.icon}>
              <FileTextOutlined />
            </div>
          </Tooltip>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.fileInfoItem}>
      <div className={fileInfoCls} onClick={handleSelect}>
        <div className={styles.icon}>
          <FileTextOutlined />
        </div>
        <Tooltip placement="right" title={fileInfo?.fileName}>
          <div className={styles.name}>{fileInfo?.fileName}</div>
        </Tooltip>
        <If when={isProcessing}>
          <div className={styles.tag}>处理中</div>
        </If>
      </div>
    </div>
  );
};

const mapStateToProps = (state: any) => ({
  /** 文档分片进程结果 */
  fileChunkProcessResult: state.fileManagement.fileChunkProcessResult,
  /** 文档列表的解析进度结果 */
  fileAnalysisResult: state.fileManagement.fileAnalysisResult,
});

export default connect(mapStateToProps)(FileInfoItem);
