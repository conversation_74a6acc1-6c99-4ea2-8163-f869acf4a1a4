/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-05-08
 * @Description  : 根据处于不同的面板取不同字段下的状态并做字段适配
 */
import type { StatusCodeType } from '@/components/FileAnalysis/config';
import type { IFileAnalysisResult } from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type { IFileChunkProcessResult } from '@/services/types/api/fileManagement/getFileChunkProcess';

import { useEffect, useState, useContext } from 'react';
import _ from 'lodash';

import { Pannels, StatusEnum } from '@/components/FileAnalysis/config';
import { CurrentPannelContext } from '@/components/FileAnalysis/PannelContext';


/**
 * 获取文件状态标签数据
 * @param fileId 文档ID
 * @param analysisResultList 文档解析结果
 * @param chunkResultList 文档分片结果
 * @returns 当前文档解析/分片的进程结果
 */
function useStatusAdapter(fileId: string, analysisResultList: IFileAnalysisResult[], chunkResultList: IFileChunkProcessResult[]): StatusCodeType {
  const { pannelName } = useContext(CurrentPannelContext);
  const [statusCode, setStatusCode] = useState<StatusCodeType>(StatusEnum.NO_NEED);


  // 文档解析页面需要使用文档解析结果进度
  useEffect(() => {
    if (pannelName === Pannels.FileAnalysisPannel) {
      const result = _.find(analysisResultList, item => item.fileId === fileId);
      setStatusCode(result?.analysisResult || StatusEnum.NO_NEED);
    }
  }, [pannelName, fileId, analysisResultList]);

  // 文档分片页面需要使用文档分片结果进度
  useEffect(() => {
    if (pannelName === Pannels.FileChunkPannel) {
      const result = _.find(chunkResultList, item => item.fileId === fileId);
      setStatusCode(result?.chunkProcess || StatusEnum.NO_NEED);
    }
  }, [pannelName, fileId, chunkResultList]);

  return statusCode;
}

export default useStatusAdapter;
