/*
 * <AUTHOR> sun<PERSON>bin<K0100008>
 * @Date         : 2025-09-02
 * @Description  : 文档切片面下-切片列表面板顶部切片校验区域
 */
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';

export interface IProps {
  /** 当前文档所有切片列表 */
  chunkList: ChunkInfoItem[];
  /** 查看未校验切片-是否勾选 */
  checkoutFlagChecked: boolean;
  /** 查看未校验切片Toggle */
  onCheckoutFlagToggle: (checked: boolean) => void;
  /** 检查切片是否可以用于一键校验 */
  onValidateChunks: () => boolean;
  /** 一键校验切片 */
  onCheckoutChunk: () => void;
}
