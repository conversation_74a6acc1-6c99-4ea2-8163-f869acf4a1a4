/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文档分片列表
 */
import type { CheckboxChangeEvent } from '@ht/sprite-ui/es/checkbox';
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { Checkbox, Button, Popconfirm } from '@ht/sprite-ui';
import { CheckCircleFilled } from '@ht-icons/sprite-ui-react';

import useCheckoutStatus from './useCheckoutStatus';

import styles from './styles.less';

const FileChunkListCheckoutArea = (props: IProps) => {
  const {
    chunkList,
    checkoutFlagChecked,
  } = props;

  const checkoutStatus = useCheckoutStatus(chunkList);

  const handleChange = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    props.onCheckoutFlagToggle(checked);
  };

  const handleCheckoutConfirm = () => {
    const validateResult = props.onValidateChunks();
    if (validateResult) {
      props.onCheckoutChunk();
    }
  };

  // 如果切片总数为0时，不展示
  if (checkoutStatus.chunkTotalCount === 0) {
    return null;
  }

  // 所有切片就已经校验
  if (checkoutStatus.allCheckedouted) {
    return (
      <div className={styles.chunkCheckoutArea}>
        <CheckCircleFilled className={styles.allCheckoutIcon} />
        <div className={styles.allCheckoutedText}>已全部校验</div>
      </div>
    );
  }

  return (
    <div className={styles.chunkCheckoutArea}>
      <div className={styles.checkoutCountText}>{checkoutStatus.checkoutCountText}</div>
      <div className={styles.line}></div>
      <Checkbox
        disabled={checkoutStatus.operateDisabled}
        checked={checkoutFlagChecked}
        onChange={handleChange}
      >
        查看未校验切片
      </Checkbox>
      <div className={styles.checkoutChunkBtn}>
        <Popconfirm
          disabled={checkoutStatus.operateDisabled}
          title="请再次确认是否全部通过校验？"
          onConfirm={handleCheckoutConfirm}
        >
          <Button
            disabled={checkoutStatus.operateDisabled}
            type="primary"
          >
            一键校验
          </Button>
        </Popconfirm>
      </div>
    </div>
  );

};

export default FileChunkListCheckoutArea;
