/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件解析面板页面
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileChunkInfo } from '@/services/types/api/fileManagement/getMarkDownChunks';
import type {  ISaveChunkResult } from '@/services/types/api/fileManagement/saveChunk';
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';
import type { withPollingProps } from '@/components/FileAnalysis/withChunkPolling';
import type { IProps, IState, IChunkListPannelRefType } from './data';

import React, { PureComponent } from 'react';
import { connect } from '@oula/oula';
import { Button, Steps, message } from '@ht/sprite-ui';
import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import { generateEffect } from '@/utils/dvaHelper';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import FileList from '@/components/FileAnalysis/FileList';
import ChunksMarkDownPreviewPannel from '@/components/FileAnalysis/ChunksMarkDownPreviewPannel';
import ChunkOriginFilePreviewPannel from '@/components/FileAnalysis/ChunkOriginFilePreviewPannel';
import FileChunkListPannel from '@/components/FileAnalysis/FileChunkListPannel';
import {
  // FileTypeEnum,
  Pannels,
  StatusEnum,
} from '@/components/FileAnalysis/config';
import { CurrentPannelContext } from '@/components/FileAnalysis/PannelContext';
import withChunkPolling from '@/components/FileAnalysis/withChunkPolling';

import styles from './styles.less';

const CurrentPannel = { pannelName: Pannels.FileChunkPannel };

const mapStateToProps = (state: any) => ({
  /** 文档列表 */
  fileInfoList: state.fileManagement.fileInfoList,
  /** 文档列表的切片进度结果 */
  fileChunkProcessResult: state.fileManagement.fileChunkProcessResult,
  /** 文档列表的解析进度结果 */
  fileAnalysisResult: state.fileManagement.fileAnalysisResult,
  /** 租户id */
  tenantId: state.global.tenantId,
});

const mapDispatchToProps = {
  /** 获取文档解析等页面需要的文档信息 */
  getFileInfoList: generateEffect('fileManagement/getFileInfoList'),
  /** 获取文件解析后的MarkDown文件内容 */
  getAnalysisMarkDown: generateEffect('fileManagement/getAnalysisMarkDown'),
  /** 获取文档切片列表 */
  getMarkDownChunks: generateEffect('fileManagement/getMarkDownChunks'),
  /** 获取源文档预览地址 */
  getPreviewUrl: generateEffect('fileManagement/getPreviewUrl'),
  /** 删除文档分片地址 */
  deleteChunk: generateEffect('fileManagement/deleteChunk'),
  /** 保存文档分片 */
  saveChunk: generateEffect('fileManagement/saveChunk'),
  /** 一键校验文档切片 */
  checkoutChunk: generateEffect('fileManagement/checkoutChunk'),
};

type PannelPropsType = IProps & withPollingProps;

class FileAnalysisChunksPannel extends PureComponent<PannelPropsType, IState> {
  fileChunkListPannelRef: React.RefObject<IChunkListPannelRefType>;

  constructor(props: PannelPropsType) {
    super(props);

    this.state = {
      previewKey: uuid(),
      currentFile: null,
      currentOriginPreviewUrl: null,
      chunkList: [],
      currentMarkDownResult: null,
      fileListCollapsed: false,
      originFileOrmarkdownPreviewCollapsed: false,
      chunkListCollapsed: false,
    };

    this.fileChunkListPannelRef = React.createRef();
  }

  componentDidMount() {
    // 初始化页面查询
    this.initPageData();
  }

  componentDidUpdate(prevProps: Readonly<PannelPropsType>): void {
    const { currentPannel: prevCurrentPannel, fileChunkProcessResult: preResult } = prevProps;
    const { currentPannel: nextCurrentPannel, fileChunkProcessResult: nextResult } = this.props;

    if (prevCurrentPannel !== nextCurrentPannel && _.last(nextCurrentPannel) === Pannels.FileChunkPannel) {
      // 如果当前展示在页面能看到的面板是【文件解析】则需要开启轮询
      const { fileIdList } = this.props;
      this.props.startPolling(fileIdList);
    }

    // NOTE: SWB 2025-05-28 如果文档切片进度变化了，则需要刷新下当前文档的切片列表
    if (preResult !== nextResult) {
      this.refreshCurrentChunkList();
    }
  }

  refreshCurrentChunkList = () => {
    const { fileChunkProcessResult } = this.props;
    const { currentMarkDownResult, currentFile } = this.state;

    // NOTE: SWB 2025-05-28 找到当前文件的切片进度结果
    const currentChunkResult = _.find(fileChunkProcessResult, item => item.fileId === currentFile?.fileId);

    if (_.isEmpty(currentMarkDownResult?.url) && currentChunkResult?.chunkProcess === StatusEnum.DONE) {
      this.getCurrentChunkList();
    }
  }

  initPageData = () => {
    const { fileIdList } = this.props;

    // 1. 初始化请求解析的文档信息
    this.props.getFileInfoList({
      fileIdList,
    }).then(this.initCurrentFile);

    // 2. 开启切片进度轮询
    this.props.startPolling(fileIdList);
  };

  initCurrentFile = () => {
    const { fileInfoList } = this.props;
    this.setState({
      currentFile: fileInfoList[0],
    }, this.doActionAfterSelectFile);
  };

  handleFileCollapsedToggle = () => {
    const { fileListCollapsed } = this.state;

    this.setState({
      fileListCollapsed: !fileListCollapsed,
    });
  };

  handleOriginOrMarkdownPreviewCollapsedToggle = () => {
    const { originFileOrmarkdownPreviewCollapsed } = this.state;

    this.setState({
      originFileOrmarkdownPreviewCollapsed: !originFileOrmarkdownPreviewCollapsed,
    });
  };

  handleChunkListCollapsed = () => {
    const { chunkListCollapsed } = this.state;

    this.setState({
      chunkListCollapsed: !chunkListCollapsed,
    });
  };

  deleteChunkInState = (chunkId: string) => {
    const { chunkList } = this.state;
    const newList = _.filter(chunkList, item => item.chunkId !== chunkId);

    this.setState({
      chunkList: this.addNoForChunkList(newList),
    });

    this.doToggleCheckoutFlagAction(newList);
  }

  doToggleCheckoutFlagAction = (list: IFileChunkInfo[]) => {
    // 判断当前是否处于【查看未校验切片】状态
    if (this.fileChunkListPannelRef.current?.checkoutFlagChecked) {
      // 找出所有【非-文章总结】并且【未校验完成】的切片列表
      const filteredList = _.filter(list, (item: IFileChunkInfo) => !item.checkoutFlag && !item.conclusion);

      if (_.isEmpty(filteredList)) {
        this.fileChunkListPannelRef.current.cancleCheckoutFlag();
      }
    }
  }

  handleDeleteChunk = async (chunkInfo: ChunkInfoItem) => {
    // 首先判断是否当前存在编辑状态的分片,如果存在不允许进行其他分片的编辑/删除/增加
    const chunkInEditing = this.findChunkInEditing();
    if (!_.isEmpty(chunkInEditing)) {
      const chunkInEditingIndex = this.findChunkIndex(chunkInEditing.chunkId);
      message.info(`分片${chunkInEditingIndex + 1}正处于编辑中，请先结束编辑！`);
      return;
    }

    const { currentFile } = this.state;

    // 1. 删除分片需要先判断该分片是否是还未保存过的临时增加的分片,如果是临时增加的直接state中删除即可
    if (chunkInfo.isTempAdd) {
      this.deleteChunkInState(chunkInfo.chunkId);
      return;
    }

    // 2. 如果该分片已经是保存到数据库中的，则需要先调用删除分片接口，成功后再删除state中对应的分片
    const deleteResult = await this.props.deleteChunk({
      fileId: currentFile?.fileId as string,
      chunkId: chunkInfo?.chunkId,
    });

    if (deleteResult) {
      this.deleteChunkInState(chunkInfo.chunkId);
    }
  }

  // 找到处于编辑状态的分片
  findChunkInEditing = () => {
    const { chunkList } = this.state;
    return _.find(chunkList, (item: ChunkInfoItem) => item.isEditing) as ChunkInfoItem | null;
  }

  handleEditChunk = (chunkInfo: ChunkInfoItem) => {
    // 首先判断是否当前存在编辑状态的分片,如果存在不允许进行其他分片的编辑/删除/增加
    const chunkInEditing = this.findChunkInEditing();
    if (!_.isEmpty(chunkInEditing)) {
      const chunkInEditingIndex = this.findChunkIndex(chunkInEditing.chunkId);
      message.info(`分片${chunkInEditingIndex + 1}正处于编辑中，请先结束编辑！`);
      return;
    }

    const { chunkList } = this.state;
    const newChunkList = _.map(chunkList, item => {
      if (item.chunkId === chunkInfo.chunkId) {
        return {
          ...item,
          isEditing: true,
        };
      }

      return item;
    });

    this.setState({
      chunkList: newChunkList,
    });
  }

  handleCloseEditChunk = (chunkInfo: ChunkInfoItem) => {
    const { chunkList } = this.state;

    const newChunkList = _.map(chunkList, item => {
      if (item.chunkId === chunkInfo.chunkId) {
        return {
          ...item,
          isEditing: false,
        };
      }

      return item;
    });

    this.setState({
      chunkList: newChunkList,
    });
  }

  findChunkIndex = (chunkId: string) => {
    const { chunkList } = this.state;
    return _.findIndex(chunkList, item => item.chunkId === chunkId);
  }

  insertTempChunk = (newChunk: ChunkInfoItem, currentChunkIndex: number, insertPosition: 'after' | 'before') => {
    const { chunkList } = this.state;

    return _.reduce(chunkList, (acc: ChunkInfoItem[], item, index) => {

      if (insertPosition === 'before' && index === currentChunkIndex) {
        acc.push(newChunk);
      }

      acc.push(item);

      if (insertPosition === 'after' && index === currentChunkIndex) {
        acc.push(newChunk);
      }

      return acc;
    }, []);

  }

  // 向当前分片的下面增加一个分片
  handleDownAddChunk = (chunkInfo: ChunkInfoItem) => {
    // 首先判断是否当前存在编辑状态的分片,如果存在不允许进行其他分片的编辑/删除/增加
    const chunkInEditing = this.findChunkInEditing();
    if (!_.isEmpty(chunkInEditing)) {
      const chunkInEditingIndex = this.findChunkIndex(chunkInEditing.chunkId);
      message.info(`分片${chunkInEditingIndex + 1}正处于编辑中，请先结束编辑！`);
      return;
    }

    // 1. 找到当前分片的位置
    const currentChunkIndex = this.findChunkIndex(chunkInfo.chunkId);
    // 2. 将新分片数据加在当前数据后
    const newChunkData: ChunkInfoItem = {
      chunkId: uuid(),
      chunkContent: '',
      chunkSummary: '',
      conclusion: false,
      checkoutFlag: false,
      isEditing: true,
      isTempAdd: true,
    };
    const newChunkList = this.insertTempChunk(newChunkData, currentChunkIndex, 'after');

    this.setState({
      chunkList: this.addNoForChunkList(newChunkList),
    }, () => {
      this.scrollNewChunkIntoView(newChunkData);
    });
  }

  // 向当前分片的上面增加一个分片
  handleUpAddChunk = (chunkInfo: ChunkInfoItem) => {
    // 首先判断是否当前存在编辑状态的分片,如果存在不允许进行其他分片的编辑/删除/增加
    const chunkInEditing = this.findChunkInEditing();
    if (!_.isEmpty(chunkInEditing)) {
      const chunkInEditingIndex = this.findChunkIndex(chunkInEditing.chunkId);
      message.info(`分片${chunkInEditingIndex + 1}正处于编辑中，请先结束编辑！`);
      return;
    }

    // 1. 找到当前分片的位置
    const currentChunkIndex = this.findChunkIndex(chunkInfo.chunkId);
    // 2. 将新分片数据加在当前数据前
    const newChunkData: ChunkInfoItem = {
      chunkId: uuid(),
      chunkContent: '',
      chunkSummary: '',
      conclusion: false,
      checkoutFlag: false,
      isEditing: true,
      isTempAdd: true,
    };
    const newChunkList = this.insertTempChunk(newChunkData, currentChunkIndex, 'before');

    this.setState({
      chunkList: this.addNoForChunkList(newChunkList),
    }, () => {
      this.scrollNewChunkIntoView(newChunkData);
    });
  }

  scrollNewChunkIntoView = (chunkInfo: ChunkInfoItem) => {
    const { currentFile } = this.state;

    const elementId = `${currentFile?.fileId || ''}-${chunkInfo.chunkId}`;

    window.setTimeout(() => {
      const chunkElement = document.getElementById(elementId);
      chunkElement?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 30);
  }

  findChunkLinkedIds = (chunkId: string) => {
    const { chunkList } = this.state;
    // 当前分片的位置
    const currentIndex = this.findChunkIndex(chunkId);
    let prevChunkId = null;
    let nextChunkId = null;

    // 1. 往前找到第一个已经保存过的分片的ID
    if (currentIndex > 0) {
      let prevChunkIndex = currentIndex - 1;

      while (_.isEmpty(prevChunkId) && prevChunkIndex >= 0) {
        const chunkInfo = chunkList[prevChunkIndex];
        if (!chunkInfo.isTempAdd) {
          prevChunkId = chunkInfo.chunkId;
        }

        prevChunkIndex--;
      }
    }

    // 2. 往后找到第一个已经保存过的分片的ID
    const lastChunkIndex = chunkList.length - 1;
    if (currentIndex < lastChunkIndex) {
      let nextChunkIndex = currentIndex + 1;

      while (_.isEmpty(nextChunkId) && nextChunkIndex <= lastChunkIndex) {
        const chunkInfo = chunkList[nextChunkIndex];
        if (!chunkInfo.isTempAdd) {
          nextChunkId = chunkInfo.chunkId;
        }

        nextChunkIndex++;
      }
    }

    return { prevChunkId, nextChunkId };
  }

  // 保存分片
  handleSaveChunk = (chunkInfo: ChunkInfoItem) => {
    const { currentFile } = this.state;

    // 1. 需要先找到当前分片的前一个分片和后一个分片的ID
    const { prevChunkId, nextChunkId } = this.findChunkLinkedIds(chunkInfo.chunkId);

    this.props.saveChunk({
      fileId: currentFile?.fileId || '',
      chunkId: chunkInfo.isTempAdd ? null : chunkInfo.chunkId,
      chunkContent: chunkInfo.chunkContent || '',
      chunkSummary: chunkInfo.chunkSummary || '',
      tenantId: this.props.tenantId,
      prevChunkId,
      nextChunkId,
    }).then(this.doAfterSaveChunk);
  }

  doAfterSaveChunk = async (result: ISaveChunkResult) => {
    if (_.isEmpty(result.chunkId)) {
      return;
    }

    // 保存成功后需要重新获取切片列表，并且需要判断如果当前处于【查看未校验切片】状态，则需要判断所有切片是否都校验完成
    const { currentFile } = this.state;

    const newChunkList = await this.props.getMarkDownChunks({ fileId: currentFile?.fileId as string });

    // NOTE: SWB 2025-09-02 需要给chunkList添加no序号这个字段
    this.setState({
      chunkList: this.addNoForChunkList(newChunkList),
    });

    this.doToggleCheckoutFlagAction(newChunkList);
  }

  // 一键校验前的切片状态校验
  handleValidateChunks = () => {
    // 首先判断是否当前存在编辑状态的分片,如果存在不允许进行其他分片的编辑/删除/增加
    const chunkInEditing = this.findChunkInEditing();
    if (!_.isEmpty(chunkInEditing)) {
      const chunkInEditingIndex = this.findChunkIndex(chunkInEditing.chunkId);
      message.info(`分片${chunkInEditingIndex + 1}正处于编辑中，请先结束编辑！`);
      return false;
    }

    return true;
  }

  // 一键校验文档切片
  handleCheckoutChunk = () => {
    const { currentFile } = this.state;

    this.props.checkoutChunk({
      fileId: currentFile?.fileId || '',
    }).then(this.doCheckoutChunkAfter);
  }

  doCheckoutChunkAfter = () => {
    this.getCurrentChunkList();

    if (this.fileChunkListPannelRef.current) {
      this.fileChunkListPannelRef.current.cancleCheckoutFlag();
    }
  }

  handleFileSelect = (fileInfo: IFileInfo) => {
    this.setState({
      currentFile: fileInfo,
      // NOTE: SWB 2025-04-16 切换文档先将解析结果MarkDown清空掉
      currentMarkDownResult: null,
      // NOTE: SWB 2025-04-16 切换文档先将文档分片清空
      chunkList: [],
      // NOTE: SWB 2025-04-16 切换文档将阅览组件用的Key清掉一遍能够重新渲染
      previewKey: uuid(),
      // NOTE: SWB 2025-04-23 切换文档清空原始文档预览地址
      currentOriginPreviewUrl: null,
    }, this.doActionAfterSelectFile);
  }

  doActionAfterSelectFile = () => {
    // const { currentFile } = this.state;

    // NOTE: SWB 2025-09-02 需求要求:【文件切片】中间部分均展示【原始文档预览】,因此不需要再查询MarkDown结果
    // this.getAnalysisMarkdownResult();
    this.getCurrentChunkList();

    // NOTE: SWB 2025-09-02 需求要求:【文件切片】中间部分均展示【原始文档预览】,因此都需要获取源文档url
    this.getCurrentOriginPreviewUrl();

    // // 1. 判断是否PDF
    // const isPDF = currentFile?.fileType === FileTypeEnum.PDF;
    // // NOTE: SWB 2025-04-23 如果是非PDF文档则需要获取原始文档的url
    // if (!isPDF) {
    //   this.getCurrentOriginPreviewUrl();
    // }
  }

  getCurrentOriginPreviewUrl = () => {
    const { currentFile } = this.state;

    this.props.getPreviewUrl({
      fileId: currentFile?.fileId as string,
    }).then((previewUrls) => {
      this.setState({
        currentOriginPreviewUrl: previewUrls?.previewUrlForEip,
      });
    });
  }

  getAnalysisMarkdownResult = () => {
    const { currentFile } = this.state;
    this.props.getAnalysisMarkDown({
      fileId: currentFile?.fileId as string,
    }).then((result) => {
      this.setState({
        currentMarkDownResult: result,
      });
    });
  }

  addNoForChunkList = (list: IFileChunkInfo[]):  ChunkInfoItem[] => {
    const newList = _.map(list, (item, index) => {
      const no = `${index + 1}`;

      return {
        ...item,
        no,
      };
    });

    return newList;
  }

  getCurrentChunkList = () => {
    const { currentFile } = this.state;
    this.props.getMarkDownChunks({
      fileId: currentFile?.fileId as string,
    }).then((result) => {
      // NOTE: SWB 2025-09-02 需要给chunkList添加no序号这个字段
      this.setState({
        chunkList: this.addNoForChunkList(result),
      });
    });
  }

  handlePrevStep = () => {
    this.props.clearPolling();
    this.props.onPrevStep();
  }

  handleNextStep = () => {
    this.props.stopPolling();
    this.props.onNextStep();
  }

  render() {
    const { fileInfoList, fileAnalysisResult } = this.props;

    const {
      previewKey,
      currentFile,
      chunkList,
      fileListCollapsed,
      originFileOrmarkdownPreviewCollapsed,
      chunkListCollapsed,
      currentMarkDownResult,
      currentOriginPreviewUrl,
    } = this.state;

    // 1. 判断是否PDF
    // NOTE: SWB 2025-09-02 需求要求:【文件切片】中间部分均展示【原始文档预览】
    const showMarkDownPreview = false;
    // const isPDF = currentFile?.fileType === FileTypeEnum.PDF;

    return (
      <CurrentPannelContext.Provider value={CurrentPannel}>
        <div className={styles.fileAnalysisChunksPannel}>
          <TopTitleInfo title="文件导入" rightInfo={null} />
          <div className={styles.processArea}>
            <Steps current={1}>
              <Steps.Step title="文件上传及接入" />
              {/* <Steps.Step title="文件解析" /> */}
              <Steps.Step title="文件切片" />
              {/* <Steps.Step title="数据处理" /> */}
              <Steps.Step title="命中测试" />
            </Steps>
          </div>
          <div className={styles.threePannels}>
            <FileList
              currentFile={currentFile}
              fileInfoList={fileInfoList}
              fileListCollapsed={fileListCollapsed}
              onCollapsedToggle={this.handleFileCollapsedToggle}
              onSelectFile={this.handleFileSelect}
            />
            {
              showMarkDownPreview
                ? (
                  <ChunksMarkDownPreviewPannel
                    key={`${previewKey}-chunkMarkDown`}
                    currentMarkDownResult={currentMarkDownResult}
                    currentFile={currentFile}
                    fileAnalysisResult={fileAnalysisResult}
                    fileListCollapsed={fileListCollapsed}
                    chunkListCollapsed={chunkListCollapsed}
                    markdownPreviewCollapsed={originFileOrmarkdownPreviewCollapsed}
                    onCollapsedToggle={this.handleOriginOrMarkdownPreviewCollapsedToggle}
                  />
                )
                : (
                  <ChunkOriginFilePreviewPannel
                    key={`${previewKey}-originPreview`}
                    currentFile={currentFile}
                    currentOriginPreviewUrl={currentOriginPreviewUrl}
                    fileListCollapsed={fileListCollapsed}
                    chunkListCollapsed={chunkListCollapsed}
                    originFilePreviewCollapsed={originFileOrmarkdownPreviewCollapsed}
                    onCollapsedToggle={this.handleOriginOrMarkdownPreviewCollapsedToggle}
                  />
                )
            }
            <FileChunkListPannel
              ref={this.fileChunkListPannelRef}
              key={currentFile?.fileId}
              chunkList={chunkList}
              currentFile={currentFile}
              fileListCollapsed={fileListCollapsed}
              originFileOrmarkdownPreviewCollapsed={originFileOrmarkdownPreviewCollapsed}
              chunkListCollapsed={chunkListCollapsed}
              onCollapsedToggle={this.handleChunkListCollapsed}
              onDeleteChunk={this.handleDeleteChunk}
              onEditChunk={this.handleEditChunk}
              onCloseEdit={this.handleCloseEditChunk}
              onDownAddChunk={this.handleDownAddChunk}
              onUpAddChunk={this.handleUpAddChunk}
              onSaveChunk={this.handleSaveChunk}
              onCheckoutChunk={this.handleCheckoutChunk}
              onValidateChunks={this.handleValidateChunks}
            />
          </div>
          <div className={styles.btnArea}>
            <Button onClick={this.handlePrevStep}>上一步</Button>
            <Button type="primary" onClick={this.handleNextStep}>下一步</Button>
          </div>
        </div>
      </CurrentPannelContext.Provider>
    );
  }
}

// @ts-expect-error
export default connect(mapStateToProps, mapDispatchToProps)(withChunkPolling(FileAnalysisChunksPannel));
