/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 召回结果项
 */
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { Tooltip } from '@ht/sprite-ui';
import { DoubleRightOutlined } from '@ht-icons/sprite-ui-react';

import styles from './styles.less';

const RecallResultItem = (props: IProps) => {
  const { data } = props;

  const title = `序号${data.orderNum}`;
  const similarityValuePercent = (data?.similarityValue || 0) * 100 || 0;
  const sliderText = `相似度：${_.toInteger(similarityValuePercent)}%`;
  const sliderBlueWidth = `${similarityValuePercent}px`;
  const chunkText = `分片${data.chunkOrder}`;

  return (
    <div className={styles.recallResultItem} onClick={() => props.onClick(data)}>
      <div className={styles.header}>
        <div className={styles.title}>{title}</div>
        <div className={styles.sliderContent}>
          <div className={styles.sliderLine}>
            <div style={{ width: sliderBlueWidth }}></div>
          </div>
          <div className={styles.sliderText}>{sliderText}</div>
        </div>
      </div>
      <div className={styles.content}>{data.content}</div>
      <div className={styles.footer}>
        <Tooltip title={`${data.fileName || ''} 》${chunkText}`}>
          <div className={styles.nameLine}>
            <div className={styles.fileName}>{data.fileName}</div>
            <div className={styles.icon}><DoubleRightOutlined /></div>
            <div className={styles.chunkText}>{chunkText}</div>
          </div>
        </Tooltip>
      </div>
    </div>
  );
};

export default RecallResultItem;
