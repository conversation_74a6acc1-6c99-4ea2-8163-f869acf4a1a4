.recallResultItem {
  width: 100%;
  height: 178px;
  background: #F6F6F7;
  border-radius: 2px;
  box-sizing: border-box;
  cursor: pointer;

  .header {
    width: 100%;
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;

    .title {
      flex: 0 0 auto;
      height: 22px;
      font-size: 14px;
      font-weight: bold;
      color: #3f434b;
      line-height: 22px;
    }

    .sliderContent {
      flex: 0 0 auto;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .sliderLine {
        flex: 0 0 auto;
        width: 120px;
        height: 6px;
        background: #dddee0;
        border-radius: 3px;
        overflow: hidden;

        div {
          width: 0;
          height: 6px;
          background: #006bff;
          border-radius: 3px;
        }
      }

      .sliderText{
        flex: 0 0 auto;
        margin-left: 20px;
        height: 22px;
        font-size: 14px;
        color: #6c6f76;
        line-height: 22px;
      }
    }
  }

  .content {
    width: 100%;
    height: 66px;
    padding: 0 16px;
    margin: 8px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    box-sizing: border-box;
    font-size: 14px;
    color: #999ba0;
    line-height: 22px;
  }

  .footer {
    padding: 13px 16px;
    width: 100%;
    height: 48px;
    box-sizing: border-box;

    .nameLine {
      width: fit-content;
      max-width: 100%;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
    }

    .fileName {
      flex: 0 1 auto;
      font-size: 14px;
      color: #006bff;
      line-height: 22px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .icon {
      flex: 0 0 auto;
      margin: 0 6px;
      font-size: 14px;
      color: #006bff;
    }

    .chunkText {
      flex: 0 0 auto;
      font-size: 14px;
      color: #006bff;
      line-height: 22px;
    }
  }
}
