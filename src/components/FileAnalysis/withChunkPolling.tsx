/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-05-28
 * @Description  : 开启【文件切片进度】接口的轮询
 */
import type {
  IFileChunkProcessResult,
  IGetFileChunkProcess,
} from '@/services/types/api/fileManagement/getFileChunkProcess';

import React from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';
import hoistStatics from 'hoist-non-react-statics';

import { generateEffect } from '@/utils/dvaHelper';
import { StatusEnum } from '@/components/FileAnalysis/config';

interface IEnhanceProps {
  /** 获取文件切片进度的结果 */
  getFileChunkProcess: (query: IGetFileChunkProcess['Query']) => Promise<IFileChunkProcessResult[]>;
}

export interface withPollingProps {
  /** 开启轮询 */
  startPolling: (fileIdList: string[]) => void;
  /** 停止轮询 */
  stopPolling: () => void;
  /** 清除轮询定时器 */
  clearPolling: () => void;
}

/** 轮询定时器ID */
let pollingIntervalId = -1;

function withChunkPolling<P extends withPollingProps>(WComp: React.ComponentType<P>): React.ComponentType<P> {

  const mapDispatchToProps = {
    /** 获取文件的切片进度结果 */
    getFileChunkProcess: generateEffect('fileManagement/getFileChunkProcess', { loading: false }),
  };

  class PollingComponent extends React.PureComponent<IEnhanceProps> {
    componentWillUnmount(): void {
      this.clearPolling();
    }

    // 判断切片是否完成
    isDone = (item: IFileChunkProcessResult) => {
      const isDone = item.chunkProcess === StatusEnum.DONE;
      const isNoNeed = item.chunkProcess === StatusEnum.NO_NEED;

      return isDone || isNoNeed;
    }

      doIntervalQueryProcess = (fileAnalysisResult: IFileChunkProcessResult[], fileIdList: string[]) => {
        // 结果中只要有一个数据没有处理完成则开启定时轮询任务
        const hasProcessingFile = _.some(fileAnalysisResult, (item: IFileChunkProcessResult) => !this.isDone(item));

        if (hasProcessingFile) {
          // 每5秒查询下
          pollingIntervalId = window.setTimeout(() => this.getFileChunkProcess(fileIdList), 5 * 1000);
        } else if (pollingIntervalId !== -1) {
          this.clearPolling();
        }
      }

      getFileChunkProcess = (fileIdList: string[]) => {
        this.props.getFileChunkProcess({
          fileIdList,
        }).then((data: IFileChunkProcessResult[]) => this.doIntervalQueryProcess(data, fileIdList));
      }

      startPolling = (fileIdList: string[]) => {
        if (pollingIntervalId === -1) {
          // 如果没有启动则查询结果后启动定时器
          this.getFileChunkProcess(fileIdList);
        }
      }

      stopPolling = () => {
        this.clearPolling();
      }

      clearPolling = () => {
        if (pollingIntervalId !== -1) {
          window.clearTimeout(pollingIntervalId);
          pollingIntervalId = -1;
        }
      }

      render() {
        const { getFileChunkProcess, ...restProps } = this.props;

        const mergedProps = {
          ...restProps,
          startPolling: this.startPolling,
          stopPolling: this.stopPolling,
          clearPolling: this.clearPolling,
        } as P;

        return (<WComp {...mergedProps} />);
      }
  }

  // @ts-expect-error
  return hoistStatics(connect(undefined, mapDispatchToProps)(PollingComponent), WComp);
};

export default withChunkPolling;
