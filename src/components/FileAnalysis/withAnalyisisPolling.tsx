/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-29
 * @Description  : 开启【文件解析结果】接口的轮询
 */
import type {
  IFileAnalysisResult,
  IGetFileAnalysisResult,
} from '@/services/types/api/fileManagement/getFileAnalysisResult';

import React from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';
import hoistStatics from 'hoist-non-react-statics';

import { generateEffect } from '@/utils/dvaHelper';
import { StatusEnum } from '@/components/FileAnalysis/config';

interface IEnhanceProps {
  /** 获取文件的解析结果 */
  getFileAnalysisResult: (query: IGetFileAnalysisResult['Query']) => Promise<IFileAnalysisResult[]>;
}

export interface withPollingProps {
  /** 开启轮询 */
  startPolling: (fileIdList: string[]) => void;
  /** 停止轮询 */
  stopPolling: () => void;
  /** 清除轮询定时器 */
  clearPolling: () => void;
}

/** 轮询定时器ID */
let pollingIntervalId = -1;

function withAnalyisisPolling<P extends withPollingProps>(WComp: React.ComponentType<P>): React.ComponentType<P> {

  const mapDispatchToProps = {
    /** 获取文件的解析结果 */
    getFileAnalysisResult: generateEffect('fileManagement/getFileAnalysisResult', { loading: false }),
  };

  class PollingComponent extends React.PureComponent<IEnhanceProps> {
    componentWillUnmount(): void {
      this.clearPolling();
    }

    // 判断文件是否解析完成
    isDone = (item: IFileAnalysisResult) => {
      const isDone = item.analysisResult === StatusEnum.DONE;
      const isNoNeed = item.analysisResult === StatusEnum.NO_NEED;

      return isDone || isNoNeed;
    }

      doIntervalQueryProcess = (fileAnalysisResult: IFileAnalysisResult[], fileIdList: string[]) => {
        // 结果中只要有一个数据没有处理完成则开启定时轮询任务
        const hasProcessingFile = _.some(fileAnalysisResult, (item: IFileAnalysisResult) => !this.isDone(item));

        if (hasProcessingFile) {
          // 每5秒查询下
          pollingIntervalId = window.setTimeout(() => this.getFileAnalysisResult(fileIdList), 5 * 1000);
        } else if (pollingIntervalId !== -1) {
          this.clearPolling();
        }
      }

      getFileAnalysisResult = (fileIdList: string[]) => {
        this.props.getFileAnalysisResult({
          fileIdList,
        }).then((data: IFileAnalysisResult[]) => this.doIntervalQueryProcess(data, fileIdList));
      }

      startPolling = (fileIdList: string[]) => {
        if (pollingIntervalId === -1) {
          // 如果没有启动则查询结果后启动定时器
          this.getFileAnalysisResult(fileIdList);
        }
      }

      stopPolling = () => {
        this.clearPolling();
      }

      clearPolling = () => {
        if (pollingIntervalId !== -1) {
          window.clearTimeout(pollingIntervalId);
          pollingIntervalId = -1;
        }
      }

      render() {
        const { getFileAnalysisResult, ...restProps } = this.props;

        const mergedProps = {
          ...restProps,
          startPolling: this.startPolling,
          stopPolling: this.stopPolling,
          clearPolling: this.clearPolling,
        } as P;

        return (<WComp {...mergedProps} />);
      }
  }

  // @ts-expect-error
  return hoistStatics(connect(undefined, mapDispatchToProps)(PollingComponent), WComp);
};

export default withAnalyisisPolling;
