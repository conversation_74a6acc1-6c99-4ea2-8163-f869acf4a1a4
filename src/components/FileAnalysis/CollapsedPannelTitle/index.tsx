/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-15
 * @Description  : 标题组件
 */
import type { IProps } from './data.d';

import React from 'react';
import _ from 'lodash';
import { IndentOutlined } from '@ht-icons/sprite-ui-react';

import styles from './styles.less';

const CollapsedPannelTitle = (props: IProps) => {
  const { title } = props;

  return (
    <div className={styles.pannelTitle}>
      <div className={styles.icon} onClick={props.onCollapsedToggle}>
        <IndentOutlined />
      </div>
      <div className={styles.text}>{title}</div>
    </div>
  );
};

export default CollapsedPannelTitle;
