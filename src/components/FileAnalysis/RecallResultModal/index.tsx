/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 召回结果项的弹框
 */
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { Tooltip, Modal } from '@ht/sprite-ui';

import styles from './styles.less';

const RecallResultItem = (props: IProps) => {
  const { data, visible } = props;

  if (!visible) {
    return null;
  }

  const title = `序号${data?.orderNum || 1}`;
  const similarityValuePercent = (data?.similarityValue || 0) * 100 || 0;
  const sliderText = `相似度：${_.toInteger(similarityValuePercent)}%`;
  const sliderBlueWidth = `${similarityValuePercent}px`;
  const chunkText = `分片${data?.chunkOrder || 1}`;

  return (
    <Modal
      open={true}
      title={title}
      width={640}
      onCancel={props.onClose}
      footer={null}
    >
      <div className={styles.modalContent}>
        <div className={styles.header}>
          <Tooltip title={`${data?.fileName || ''} 》${chunkText}`}>
            <div className={styles.fileName}>{data?.fileName}</div>
          </Tooltip>
          <div className={styles.sliderContent}>
            <div className={styles.sliderLine}>
              <div style={{ width: sliderBlueWidth }}></div>
            </div>
            <div className={styles.sliderText}>{sliderText}</div>
          </div>
        </div>
        <div className={styles.content}>{data?.content}</div>
      </div>
    </Modal>
  );
};

export default RecallResultItem;
