/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-16
 * @Description  : MD文档阅览
 */
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { Spin, Empty } from '@ht/sprite-ui';

import EmptyPng from '@/assets/empty.png';
import { StatusEnum } from '@/components/FileAnalysis/config';
import MdEditor from '@/components/FileAnalysis/FileUrlMdEditor';

import styles from './styles.less';

function AnalysisMarkdownFilePreview(props: IProps) {

  const {
    mdEditorView,
    currentAnalysisResult,
    currentMarkDownResult,
  } = props;

  // 判断当前解析后的MD文件的结果
  const isUnStart = currentAnalysisResult === StatusEnum.UN_START;
  const isProcessing = currentAnalysisResult === StatusEnum.PROCESSING;
  const isDone = currentAnalysisResult === StatusEnum.DONE;

  // 1. 如果是正在解析中，展示loading组件
  if (isUnStart || isProcessing) {
    return (
      <div className={styles.nopreview}>
        <Spin tip="文档解析中，请稍等" />
      </div>
    );
  }

  // 2. 如果是解析成功的则展示MarkDown文件预览
  if (isDone && !_.isEmpty(currentMarkDownResult?.url)) {
    return (
      <div className={styles.markdownArea}>
        <MdEditor
          mdEditorView={mdEditorView}
          fileUrl={currentMarkDownResult?.url || ''}
          onChange={props.onChange}
        />
      </div>
    );
  }

  // 3. 如果处于其他状态下展示不能支持预览
  return (
    <div className={styles.nopreview}>
      <Empty description="该格式暂无法支持解析" image={EmptyPng} />
    </div>
  );
}

export default React.memo(AnalysisMarkdownFilePreview);
