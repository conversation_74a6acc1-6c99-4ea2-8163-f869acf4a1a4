/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 命中测试面板
 */
import type { IRecallTestResult } from '@/services/types/api/fileManagement/recallTest';
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IProps, IState } from './data';

import React, { PureComponent } from 'react';
import { connect } from '@oula/oula';
import { Button, Steps, Input, Empty } from '@ht/sprite-ui';
import _ from 'lodash';

import { generateEffect } from '@/utils/dvaHelper';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import ThresholdValueBlock from '@/components/FileAnalysis/ThresholdValueBlock';
import RecallTestFileList from '@/components/FileAnalysis/RecallTestFileList';
import RecallResultItem from '@/components/FileAnalysis/RecallResultItem';
import RecallResultModal from '@/components/FileAnalysis/RecallResultModal';
import If from '@/components/common/If';
import { Pannels } from '@/components/FileAnalysis/config';
import EmptyPng from '@/assets/empty.png';

import styles from './styles.less';

const mapStateToProps = (state: any) => ({
  /** 文档列表 */
  fileInfoList: state.fileManagement.fileInfoList,
  /** 文档列表的解析进度结果 */
  fileAnalysisResult: state.fileManagement.fileAnalysisResult,
});

const mapDispatchToProps = {
  /** 获取文档解析等页面需要的文档信息 */
  getFileInfoList: generateEffect('fileManagement/getFileInfoList'),
  /** 召回测试 */
  recallTest: generateEffect('fileManagement/recallTest'),
};

class FileRecallTestPannel extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {
      showPrevStepBtn: false,
      currentFile: null,
      keyword: '',
      thresholdValue: 0,
      recallResult: [],
      openModal: false,
      selectedRecallResult: null,
    };
  }

  componentDidMount() {
    this.setPrevStepBtnVisible();
    // 初始化页面查询
    this.initPageData();
  }

  componentDidUpdate(prevProps: IProps): void {
    const { currentPannel: prevPannel } = prevProps;
    const { currentPannel: nextPannel } = this.props;

    if (prevPannel !== nextPannel) {
      this.setPrevStepBtnVisible();
    }
  }

  isFromFileListPannelEnter = () => {
    const { currentPannel } = this.props;

    // 如果当前面板只有两个，并且【文件列表】【命中测试】,所以只需要判断第二个面板是否是【命中测试】
    return _.nth(currentPannel, 1) === Pannels.FileRecallTestPannel;
  }

  setPrevStepBtnVisible = () => {
    // NOTE: SWB 2025-06-10 如果是从【文件列表】点击【命中测试】按钮进入，则不展示【上一步】
    // 如果是从【文件切片】点击【下一步】进入，则展示【上一步】
    const isFromFileListPannelEnter = this.isFromFileListPannelEnter();

    this.setState({
      showPrevStepBtn: !isFromFileListPannelEnter,
    });
  }

  initPageData = () => {
    const { fileIdList, fileInfoList } = this.props;

    if (_.isEmpty(fileInfoList)) {
      // 1. 初始化请求解析的文档信息
      this.props.getFileInfoList({
        fileIdList,
      }).then(this.initCurrentFile);
    } else {
      this.initCurrentFile();
    }
  };

  initCurrentFile = () => {
    const { fileInfoList } = this.props;

    this.setState({
      currentFile: fileInfoList[0],
    });
  };

  handlePrevStep = () => {
    this.props.onPrevStep();
  }

  handleComplete = () => {
    // TODO: SWB 2025-04-28 后续添加【完成】回调的前置逻辑
    this.props.onComplete();
  }

  handleThresholdValueChange = (thresholdValue: number | null) => {
    // NOTE: SWB 2025-06-09 测试要求只能输入两位小数，超过不给输入
    const valueReg = /^0\.?\d{0,2}$/;

    if (thresholdValue === null || valueReg.test(`${thresholdValue}`)) {
      this.setState({
        thresholdValue,
      });
    }
  }

  handleFileSelect = (fileInfo: IFileInfo) => {
    this.setState({
      currentFile: fileInfo,
    });
  }

  doRecallTestAction = () => {
    const { fileIdList } = this.props;
    const { thresholdValue, keyword } = this.state;

    this.props.recallTest({
      thresholdValue,
      keyword,
      fileIdList,
    }).then((recallResult) => {
      this.setState({ recallResult });
    });
  }

  handleRecallTestClick = () => {
    this.doRecallTestAction();
  }

  handleKeywordTextAreaPressEnter = () => {
    this.doRecallTestAction();
  }

  handleKeywordChange:React.ChangeEventHandler<HTMLTextAreaElement>  = (e) => {
    const { value } = e.target;
    const keyword = _.trim(value);

    this.setState({
      keyword,
    });
  }

  handleRecallResultItemClick = (data: IRecallTestResult) => {
    this.setState({
      selectedRecallResult: data,
      openModal: true,
    });
  }

  handleRecallResultModalClose = () => {
    this.setState({
      openModal: false,
      selectedRecallResult: null,
    });
  }

  renderRecallResult = () => {
    const { recallResult } = this.state;

    if (_.isEmpty(recallResult)) {
      return (
        <div className={styles.noResult}>
          <Empty description="暂无数据" image={EmptyPng} />
        </div>
      );
    }

    return (
      <div className={styles.resultData}>
        {_.map(recallResult, item => {
          const { fileId, chunkOrder } = item;

          return (
            <RecallResultItem
              key={`${fileId}-${chunkOrder}`}
              data={item}
              onClick={this.handleRecallResultItemClick}
            />
          );
        })}
      </div>
    );
  }

  render() {
    const {
      fileInfoList,
    } = this.props;

    const {
      showPrevStepBtn,
      thresholdValue,
      currentFile,
      keyword,
      openModal,
      selectedRecallResult,
    } = this.state;

    return (
      <div className={styles.fileRecallTestPannel}>
        <TopTitleInfo title="文件导入" rightInfo={null} />
        <div className={styles.processArea}>
          <Steps current={2}>
            <Steps.Step title="文件上传及接入" />
            {/* <Steps.Step title="文件解析" /> */}
            <Steps.Step title="文件切片" />
            {/* <Steps.Step title="数据处理" /> */}
            <Steps.Step title="命中测试" />
          </Steps>
        </div>
        <div className={styles.contentWrap}>
          <div className={styles.leftArea}>
            <ThresholdValueBlock
              thresholdValue={thresholdValue}
              onChange={this.handleThresholdValueChange}
            />
            <RecallTestFileList
              currentFile={currentFile}
              fileInfoList={fileInfoList}
              onSelectFile={this.handleFileSelect}
            />
          </div>
          <div className={styles.rightArea}>
            <div className={styles.inputHeader}>
              <div className={styles.headerTitle}>输入</div>
              <div className={styles.inputBtnArea}>
                <Button type="primary" onClick={this.handleRecallTestClick}>召回测试</Button>
              </div>
            </div>
            <div className={styles.keywordArea}>
              <Input.TextArea
                className={styles.keywordTextArea}
                value={keyword}
                onChange={this.handleKeywordChange}
                onPressEnter={this.handleKeywordTextAreaPressEnter}
              />
            </div>
            <div className={styles.resultArea}>
              <div className={styles.resultTitle}>召回结果</div>
              <div className={styles.resultList}>
                {this.renderRecallResult()}
              </div>
            </div>
          </div>
        </div>
        <RecallResultModal
          visible={openModal}
          data={selectedRecallResult}
          onClose={this.handleRecallResultModalClose}
        />
        <div className={styles.btnArea}>
          <If when={showPrevStepBtn}>
            <Button onClick={this.handlePrevStep}>上一步</Button>
          </If>
          <Button type="primary" onClick={this.handleComplete}>完成</Button>
        </div>
      </div>
    );
  }
}

// @ts-expect-error
export default connect(mapStateToProps, mapDispatchToProps)(FileRecallTestPannel);
