/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 命中测试的文件列表
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileAnalysisResult } from '@/services/types/api/fileManagement/getFileAnalysisResult';

export interface IProps {
  /** 当前选则的文档 */
  currentFile: IFileInfo | null;
  /** 文档信息列表 */
  fileInfoList: IFileInfo[];
  /** 切换文档 */
  onSelectFile: (fileInfo: IFileInfo) => void;
}
