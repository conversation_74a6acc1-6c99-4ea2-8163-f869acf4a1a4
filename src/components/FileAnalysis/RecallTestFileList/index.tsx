/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-28
 * @Description  : 命中测试的文件列表
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IProps } from './data';

import React, { useCallback } from 'react';
import _ from 'lodash';

import RecallTestFileListTitle from '@/components/FileAnalysis/RecallTestFileListTitle';
import FileInfoItem from '@/components/FileAnalysis/FileInfoItem';

import styles from './styles.less';

const RecallTestFileList = (props: IProps) => {
  const {
    fileInfoList,
  } = props;

  const renderFileItem = useCallback(
    (item: IFileInfo) => {
      const { fileId } = item;

      return (
        <FileInfoItem
          key={fileId}
          collapsed={false}
          selectable={false}
          fileInfo={item}
        />
      );
    },
    [],
  );

  return (
    <div className={styles.recallTestFileList}>
      <RecallTestFileListTitle
        title="文档列表"
      />
      <div className={styles.fileListArea}>
        {_.map(fileInfoList, renderFileItem)}
      </div>
    </div>
  );
};

export default RecallTestFileList;
