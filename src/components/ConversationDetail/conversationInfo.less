.conversationInfo {
  .title {
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #3f434b;
    position: relative;
    padding-left: 8px;

    &::before {
      content: " ";
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 14px;
      background: #006bff;
      transform: translate(0, -50%);
    }
  }

  .conversationInfoItem {
    margin-top: 34px;
    display: flex;
    width: 100%;
    padding-right: 10px;

    .label,
    .value {
      display: inline-block;
      width: 98px;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: #6c6f76;
    }

    .label {
      flex: 0 0 98px;
    }

    .value {
      flex: 1;
      width: auto;
      color: #3f434b;
    }
  }
}
