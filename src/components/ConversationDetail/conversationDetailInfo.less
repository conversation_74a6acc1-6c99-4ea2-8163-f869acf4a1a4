.conversationDetailInfo {
  width: 560px;
  height: 100%;

  .titleInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    .title {
      height: 24px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 500;
      color: #3f434b;
      position: relative;
      padding-left: 8px;

      &::before {
        content: " ";
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 14px;
        background: #006bff;
        transform: translate(0, -50%);
      }
    }
  }

  .aiChatMessage {
    width: 100%;
    height: calc(100% - 48px);
    box-shadow: 0 0 10px 0 rgba(0, 25, 128, 10%);
    border-radius: 8px;
    border: 1px solid #d0d7e2;
    background: linear-gradient(180deg, #cfe1f4 0%, #fff 100%);

    .aiChatHeader {
      height: 55px;
      padding: 0 20px;
      display: flex;
      align-items: center;

      img {
        width: 30px;
        height: 30px;
      }

      .title {
        font-size: 18px;
        font-weight: bold;
        color: #333333;
        margin-left: 10px;
      }
    }

    .aiChatContent {
      height: calc(100% - 55px);
      overflow-y: scroll;
      padding: 0 20px 15px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track-piece {
        display: none;
      }

      &::-webkit-scrollbar-thumb {
        border: 3px solid #b9d1e9;
        width: 6px;
        height: 45px;
        background: #b9d1e9;
        border-radius: 3px;
      }

      .spinSty {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
    }
  }
}
