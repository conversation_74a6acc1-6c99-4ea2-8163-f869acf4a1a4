/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：数据反馈-【所属部门】筛选组件
 */
import React, { useState } from 'react';
import { TreeSelect } from '@ht/sprite-ui';
import type { TreeSelectProps } from '@ht/sprite-ui';
import _ from 'lodash';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import type { DefaultOptionType } from '@ht/sprite-ui/lib/select';
import styles from './departmentTreeSelect.less';

const { SHOW_PARENT, SHOW_ALL } = TreeSelect;
type DepartmentTreeSelectProps = TreeSelectProps & {
  /** 数据源 */
  treeData: DefaultOptionType[];
  filterInfoChange: (filterFieldName: string, value: any) => void;
  onDepartmentChange?: (obj:any) => void;
}
const DepartmentTreeSelect: React.FC<DepartmentTreeSelectProps> = ({
  defaultValue,
  treeData,
  filterInfoChange,
  onDepartmentChange,
  ...treeSelectProps
}) => {

  const [departmentValue, setDepartmentValue] = useState<any>(defaultValue || [{ label: '不限', value: '不限' }]);
  const [showPopup, setShowPopup] = useState<boolean>(false);

  // 所属部门的显示
  const handleDepartmentTagRender = (props: CustomTagProps) => (<>{props.label}</>);

  // 所属部门选中树节点
  const handleDepartmentChange = (valueObj: any) => {
    // 过滤【不限】元素
    const newValueObj = _.filter(valueObj, item => item.label);
    const newValues = _.map(newValueObj, item => item.value);
    if (_.isEmpty(newValues)) {
      setDepartmentValue([{ label: '不限', value: '不限' }]);
      filterInfoChange('orgIdList', []);
      onDepartmentChange?.([{ label: '不限', value: '不限' }]);
      setShowPopup(false);
    } else {
      setDepartmentValue(newValueObj);
      filterInfoChange('orgIdList', newValues);
      onDepartmentChange?.(newValueObj);
      setShowPopup(true);
    }
  };

  // 所属部门展开下拉菜单的回调（搜索状态）
  const handleDropdownVisibleChange = (visible: boolean) => {
    // 展开下拉菜单时，如果是【不限】，则清空
    // 收起下拉菜单时，如果是空，则重置【不限】
    const newValue = _.map(departmentValue, item => item.value);
    if (visible && _.includes(newValue, '不限')) {
      setDepartmentValue([]);
    } else if (_.isEmpty(newValue)) {
      setDepartmentValue([{ label: '不限', value: '不限' }]);
    }
  };

  return (
    <TreeSelect
      treeData={treeData}
      treeCheckable={true}
      showSearch={true}
      bordered={false}
      // treeDefaultExpandAll={true}
      dropdownMatchSelectWidth={false}
      showCheckedStrategy={SHOW_ALL}
      treeCheckStrictly={true}
      value={departmentValue}
      showArrow={true}
      treeNodeFilterProp='orgName'
      maxTagCount='responsive'
      showPopup={showPopup}
      labelInValue={true}
      allowClear={showPopup}
      className={styles.treeSelect}
      autoClearSearchValue={false}
      fieldNames={{ label: 'orgName', value: 'orgCode' }}
      tagRender={handleDepartmentTagRender}
      onChange={handleDepartmentChange}
      onDropdownVisibleChange={handleDropdownVisibleChange}
      {...treeSelectProps}
    />
  );
};

export default DepartmentTreeSelect;
