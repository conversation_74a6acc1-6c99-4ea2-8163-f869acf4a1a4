import type { IQueryFileManagementList } from '@/services/types/api/fileManagement/queryFileManagementList';

const Response: IQueryFileManagementList['Response'] = {
  'code': '0',
  'msg': 'ok',
  'resultData': {
    'list': [
      {
        'fileId': 'S3-4e9e4eb3a33547fbabefea8d0c0a30ba',
        'fileName': '投顾签约服务类目整理 (1).pdf',
        'fileFormat': 'pdf',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '资讯产品',
            'value': 23,
          },
          {
            'label': '组合产品（股票组合，ETF 组合）',
            'value': 141,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-09-04 17:08',
        'enabled': 'N',
        'fileStatus': {
          'label': '处理中',
          'value': 'handing',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 3009,
        'relationId': '2889',
      },
      {
        'fileId': 'S3-d1c302d343ba48f0bda636a14ab0f7b8',
        'fileName': '自动化开发定位规范.docx',
        'fileFormat': 'docx',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '融资融券',
            'value': 85,
          },
          {
            'label': '机构融资与股票质押',
            'value': 105,
          },
          {
            'label': '股票质押',
            'value': 207,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-09-04 17:08',
        'enabled': 'N',
        'fileStatus': {
          'label': '处理中',
          'value': 'handing',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 3007,
        'relationId': '2887',
      },
      {
        'fileId': 'S3-482062bec0004471bef8403935d18296',
        'fileName': '数据构造平台使用说明.docx',
        'fileFormat': 'docx',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '交易',
            'value': 21,
          },
          {
            'label': '佣金',
            'value': 25,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-09-04 16:35',
        'enabled': 'N',
        'fileStatus': {
          'label': '处理中',
          'value': 'handing',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 3005,
        'relationId': '2885',
      },
      {
        'fileId': 'S3-8b2829f6192843ae91433f3d58726902',
        'fileName': '投顾签约服务类目整理.pdf',
        'fileFormat': 'pdf',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '交易',
            'value': 21,
          },
          {
            'label': '佣金',
            'value': 25,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-09-02 17:22',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2997,
        'relationId': '2883',
      },
      {
        'fileId': 'S3-be567010ac3748f18a6e377f7a6a38f1',
        'fileName': '_ST天茂关于以股东会决议方式主动终止公司股票上市的公告000627.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '专业交易',
            'value': 197,
          },
          {
            'label': 'MATIC',
            'value': 199,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-09-01 17:40',
        'enabled': 'N',
        'fileStatus': {
          'label': '处理中',
          'value': 'handing',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2995,
        'relationId': '2881',
      },
      {
        'fileId': 'S3-9d80727efc674d45bc4376d2c12c8178',
        'fileName': '睿评-时间对应规则.docx',
        'fileFormat': 'docx',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '交易',
            'value': 21,
          },
          {
            'label': '佣金',
            'value': 25,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-07-30 17:39',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2979,
        'relationId': '2871',
      },
      {
        'fileId': 'S3-0a5a7acfbabf428bb33cb59d72943b34',
        'fileName': '企微管理员圈客群发操作手册.docx',
        'fileFormat': 'docx',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '交易',
            'value': 21,
          },
          {
            'label': '佣金',
            'value': 25,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-07-10 14:55',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2971,
        'relationId': '2867',
      },
      {
        'fileId': 'S3-ec425d6f45bc43a4a94864c318809380',
        'fileName': 'jira手册.docx',
        'fileFormat': 'docx',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '账户体系',
            'value': 93,
          },
          {
            'label': '心理账户子账户业务',
            'value': 125,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-27 16:07',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2959,
        'relationId': '2865',
      },
      {
        'fileId': 'S3-799f28176b71479d864a3d82245679f3',
        'fileName': '测试更新3.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '交易',
            'value': 21,
          },
          {
            'label': '佣金',
            'value': 25,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-19 14:54',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2949,
        'relationId': '2861',
      },
      {
        'fileId': '430039',
        'fileName': '测试更新3.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '客服平台',
          'value': 'cs',
        },
        'updateTime': '2025-06-19 14:40',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2945,
        'relationId': '2857',
      },
      {
        'fileId': '430037',
        'fileName': '测试更新2.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '客服平台',
          'value': 'cs',
        },
        'updateTime': '2025-06-19 11:32',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2943,
        'relationId': '2855',
      },
      {
        'fileId': '430035',
        'fileName': '测试更新1改名啊.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '客服平台',
          'value': 'cs',
        },
        'updateTime': '2025-06-19 11:18',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2941,
        'relationId': '2853',
      },
      {
        'fileId': '430019',
        'fileName': '《测试110》.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '客服平台',
          'value': 'cs',
        },
        'updateTime': '2025-06-18 14:11',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2937,
        'relationId': '2851',
      },
      {
        'fileId': 'S3-e564f484c8794a25b6f41623db42445c',
        'fileName': '【对接人信息】专业交易服务对接人及MATIC支持和对接人信息.pdf',
        'fileFormat': 'pdf',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-12 18:55',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2903,
        'relationId': '2795',
      },
      {
        'fileId': '430001',
        'fileName': '《智能体简介》0610新增.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '客服平台',
          'value': 'cs',
        },
        'updateTime': '2025-06-10 09:57',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2899,
        'relationId': '2393',
      },
      {
        'fileId': '430002',
        'fileName': '《嫦娥六号》0610新增.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '交易',
            'value': 21,
          },
          {
            'label': '佣金',
            'value': 25,
          },
        ],
        'dataSources': {
          'label': '客服平台',
          'value': 'cs',
        },
        'updateTime': '2025-06-10 09:57',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2901,
        'relationId': '2395',
      },
      {
        'fileId': 'S3-5d76aecf95934aaaaa478f9c352203b5',
        'fileName': '计算机网络的基础知识重上传2.md',
        'fileFormat': 'md',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-07 15:08',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2891,
        'relationId': '2363',
      },
      {
        'fileId': 'S3-a634269bff524067a0034c4ef8383a9c',
        'fileName': 'login.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-06 09:57',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2885,
        'relationId': '2389',
      },
      {
        'fileId': 'S3-2b1f1960b40445479cec71dde00bb14c',
        'fileName': 'index.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': null,
            'value': null,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-06 09:44',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2883,
        'relationId': '2387',
      },
      {
        'fileId': 'S3-a1335790aa0749818d4fa655118d6925',
        'fileName': '111aA.html',
        'fileFormat': 'html',
        'version': 'V1',
        'categoryInfo': [
          {
            'label': '经纪业务',
            'value': 19,
          },
          {
            'label': '账户体系',
            'value': 93,
          },
          {
            'label': '心理账户子账户业务',
            'value': 125,
          },
        ],
        'dataSources': {
          'label': '本地',
          'value': 'bd',
        },
        'updateTime': '2025-06-05 17:34',
        'enabled': 'Y',
        'fileStatus': {
          'label': '已启用',
          'value': 'active',
        },
        'chunkVerifyStatus': {
          'label': '',
          'value': null,
        },
        'id': 2877,
        'relationId': '2385',
      },
    ],
    'page': {
      'pageSize': 20,
      'pageNum': 1,
      'totalCount': 1321,
      'totalPage': 67,
    },
  },
  'messageType': '0',
  'traceId': '86ba3a8b-4bec-4238-8119-687401e515c9',
};

export default Response;
