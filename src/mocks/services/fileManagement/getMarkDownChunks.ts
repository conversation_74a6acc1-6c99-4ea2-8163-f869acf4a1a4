import type { IGetMarkDownChunks } from '@/services/types/api/fileManagement/getMarkDownChunks';

const Response:IGetMarkDownChunks['Response'] = {
  'code': '0',
  'msg': 'ok',
  'resultData': [
    {
      'chunkId': '45867',
      'chunkContent': '1．套利交易/1.1 ETF 套利->paragraph->ETF套利是利用ETF在二级市场的市值与一级市场的净值之间的偏差进行套利',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45869',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利->paragraph->ETF自助套利包含常用ETF监控组件和套利交易组件两个部分',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45871',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表->image->常用ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45873',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45875',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45877',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45879',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45881',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45883',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.1 常用ETF列表',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45885',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.3行情数据揭示->paragraph->行情数据揭示包括沪深交易所五档行情盘口',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45887',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.3行情数据揭示',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45889',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.3行情数据揭示',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45891',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.3行情数据揭示',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45893',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.2.3行情数据揭示',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45895',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45897',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45899',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45901',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45903',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45905',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45907',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45909',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45911',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45913',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45915',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.2 ETF 自助套利/1.1.3.2 ETF',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45917',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.5 ETF 清单/1.1.5.2',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45919',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.5 ETF 清单/1.1.5.2',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
    {
      'chunkId': '45921',
      'chunkContent': '1．套利交易/1.1 ETF 套利/1.1.5 ETF 清单/1.1.5.2',
      'chunkSummary': null,
      'conclusion': false,
      'checkoutFlag': false,
    },
  ],
  'messageType': '0',
  'traceId': 'c67a1830-02b1-4538-adfb-f69b0f9989f1',
};

export default Response;
