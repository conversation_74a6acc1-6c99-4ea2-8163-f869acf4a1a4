import type { Dispatch, History } from '@oula/oula';
import type {
  FeedbackPersonResult,
  IQueryFeedbackPerson,
} from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type {
  DepartmentInfoItem,
  IQueryDepartmentInfo,
} from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type {
  DataFeedbackList,
  IQueryDataFeedbackList,
} from '@/services/types/api/dataFeedback/queryDataFeedbackList';
import type {
  ConversationInfoResult,
  IQueryConversationInfo,
} from '@/services/types/api/dataFeedback/queryConversationInfo';

export type FilterInfoDataType = {
  /** 数据类型 */
  dataType: string;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 反馈人 */
  empId: string;
  /** 所属部门 */
  orgIdList: string[];
  orgList: any[];
  /** 反馈标签 */
  feedbackTags?: string[];
  /** 反馈渠道 */
  feedbackChannels: string[];
  /** 场景覆盖 */
  sceneCoverages: string[];
  /** 涉及Agent */
  agents: string[];
  isAnalysis: string[];
  tgType: string[];
}

export type PaginationDataType = {
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
}

export type SortDataType = {
  /** 排序字段 */
  sortField: string;
  /** 排序顺序 */
  sortOrder?: string | null;
}

export interface DataFeedbackProps {
  dispatch: Dispatch;
  history: History;
  /** 反馈人 */
  feedbackPerson: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  /** 数据反馈列表 */
  dataFeedbackList: DataFeedbackList;
  /** 数据反馈列表loading */
  queryDataFeedbackListLoading: boolean;
  /** 查询反馈人loading */
  queryFeedbackPersonLoading: boolean;
  /** 会话信息 */
  conversationInfo: ConversationInfoResult;
  /** 查询反馈人 */
  queryFeedbackPerson: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  /** 查询所属部门 */
  queryDepartmentInfo: (query: IQueryDepartmentInfo['Query']) => Promise<unknown>;
  /** 查询反馈标签和Agent字典 */
  queryFeedbackTagsAndAgentDict: () => Promise<unknown>;
  /** 查询数据反馈列表 */
  queryDataFeedbackList: (query: IQueryDataFeedbackList['Query']) => Promise<unknown>;
  /** 查询会话信息 */
  queryConversationInfo: (query: IQueryConversationInfo['Query']) => Promise<unknown>;
}

export interface DataFeedbackState {
  /** 筛选条件数据 */
  filterInfoData: FilterInfoDataType;
   /** 筛选条件数据 */
  queryFilterInfoData: FilterInfoDataType;
  /** 分页数据 */
  paginationData: PaginationDataType;
  /** 排序数据 */
  sortData: SortDataType;
}
