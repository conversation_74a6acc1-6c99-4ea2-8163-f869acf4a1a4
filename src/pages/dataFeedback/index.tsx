/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-access-state-in-setstate */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-14
 * @Description：数据反馈
*/
import type { ConnectState } from '@/models/connect';
import type { TablePaginationConfig } from '@ht/sprite-ui/es/table';
import type { DataFeedbackListItem } from '@/services/types/api/dataFeedback/queryDataFeedbackList';
import type { FilterValue, SorterResult } from '@ht/sprite-ui/lib/table/interface';
import type {
  DataFeedbackProps,
  DataFeedbackState,
  FilterInfoDataType,
  PaginationDataType,
  SortDataType,
} from './data';

import React, { PureComponent } from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';
import { Tabs } from '@ht/sprite-ui';
import BaseSettings from '@config/BaseSettings';

import { generateEffect } from '@/utils/dvaHelper';
import WithCustomBreadcrumb from '@/components/common/WithCustomBreadcrumb';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import FilterInfo from '@/components/DataFeedback/FilterInfo';
import TableList from '@/components/DataFeedback/TableList';

import styles from './index.less';
import { dataFeedbackPersistenceKey } from './config';

const mapStateToProps = ({ dataFeedback, loading }: ConnectState) => ({
  /** 反馈人 */
  feedbackPerson: dataFeedback.feedbackPerson,
  /** 所属部门 */
  department: dataFeedback.department,
  /** 反馈标签和Agent字典 */
  dictData: dataFeedback.dictData,
  /** 数据反馈列表 */
  dataFeedbackList: dataFeedback.dataFeedbackList,
  // 数据反馈列表loading
  queryDataFeedbackListLoading: loading.effects['dataFeedback/queryDataFeedbackList'],
  // 查询反馈人loading
  queryFeedbackPersonLoading: loading.effects['dataFeedback/queryFeedbackPerson'],
});

const mapDispatchToProps = {
  /** 查询反馈人 */
  queryFeedbackPerson: generateEffect('dataFeedback/queryFeedbackPerson'),
  /** 查询所属部门 */
  queryDepartmentInfo: generateEffect('dataFeedback/queryDepartmentInfo'),
  /** 查询反馈标签和Agent字典 */
  queryFeedbackTagsAndAgentDict: generateEffect('dataFeedback/queryFeedbackTagsAndAgentDict'),
  /** 查询数据反馈列表 */
  queryDataFeedbackList: generateEffect('dataFeedback/queryDataFeedbackList'),
};

const persistenceKey = dataFeedbackPersistenceKey.badKey;
const storageValue = window.sessionStorage.getItem(persistenceKey);
const sessionParams = storageValue ? JSON.parse(storageValue) : {};

// @ts-expect-error
@connect(mapStateToProps, mapDispatchToProps)
class DataFeedback extends PureComponent<DataFeedbackProps, DataFeedbackState> {
  constructor(props: DataFeedbackProps) {
    super(props);
    this.state = {
      // 筛选条件数据
      filterInfoData: {
        // 开始时间
        startTime: '',
        // 结束时间
        endTime: '',
        // 反馈人
        empId: '',
        // 所属部门
        orgIdList: [],
        // 反馈标签
        feedbackTags: [],
        // 反馈渠道
        feedbackChannels: [],
        // 场景覆盖
        sceneCoverages: [],
        // 涉及Agent
        agents: [],
        // 所属部门集合
        orgList: [],
        // 是否分析
        isAnalysis: [],
        // 员工类型
        tgType: [],
        ...sessionParams?.filterParams,
        // 数据类型
        dataType: 'dislikes',
      },
      // 带入查询的筛选条件数据
      queryFilterInfoData: {
        // 开始时间
        startTime: '',
        // 结束时间
        endTime: '',
        // 反馈人
        empId: '',
        // 所属部门
        orgIdList: [],
        // 反馈标签
        feedbackTags: [],
        // 反馈渠道
        feedbackChannels: [],
        // 场景覆盖
        sceneCoverages: [],
        // 涉及Agent
        agents: [],
        // 所属部门集合
        orgList: [],
        // 是否分析
        isAnalysis: [],
        // 员工类型
        tgType: [],
        ...sessionParams?.filterParams,
        // 数据类型
        dataType: 'dislikes',
      },
      // 分页数据
      paginationData: {
        pageNum: 1,
        pageSize: 20,
        ...sessionParams?.pageParams,
      },
      // 排序数据
      sortData: {
        // 排序字段
        sortField: 'feedbackTime',
        // 排序顺序
        sortOrder: 'desc',
      },
    };
  }

  // 查询数据反馈列表
  queryDataFeedbackListInfo = (
    newFilterInfoData?: Partial<FilterInfoDataType>,
    newPaginationData?: Partial<PaginationDataType>,
    newSortData?: Partial<SortDataType>,
  ) => {
    const { queryDataFeedbackList } = this.props;
    const { queryFilterInfoData, paginationData, sortData } = this.state;
    // 最终的排序数据
    const finalSortData = {
      ...sortData,
      ...newSortData,
    };
    // 处理排序字段
    if (finalSortData.sortOrder) {
      finalSortData.sortOrder = finalSortData.sortOrder === 'ascend' ? 'asc' : 'desc';
    }
    console.log('初始化查接口', { ...queryFilterInfoData, ...newFilterInfoData });
    const { orgList } = { ...queryFilterInfoData, ...newFilterInfoData };
    const queryOrgIdList: any[] = [];
    orgList.forEach((item: { value: any; }) => {
      if (item.value !== '不限') {
        queryOrgIdList.push(item.value);
      }
    });
    // 合并请求入参
    const searchFilterInfoData = {
      ...queryFilterInfoData,
      ...paginationData,
      ...newFilterInfoData,
      ...newPaginationData,
      orgIdList: queryOrgIdList,
      ...finalSortData,
    };
    queryDataFeedbackList(searchFilterInfoData);
  };

  componentDidMount() {
    const { queryDepartmentInfo, queryFeedbackTagsAndAgentDict } = this.props;
    // 查询所属部门
    // 投顾发展部(ZZ323372) 平台运营部(ZZ500107) 财富管理部(ZZ001041) 信息技术部(ZZ001014) 融资融券部(ZZ001112) 金融产品部(zz001128)
    queryDepartmentInfo({ hqOrgCodeList: ['ZZ323372', 'ZZ500107', 'ZZ001041', 'ZZ001014', 'ZZ001112', 'zz001128'] });
    // 查询反馈标签和Agent字典
    queryFeedbackTagsAndAgentDict();
    // 查询数据反馈列表
    this.queryDataFeedbackListInfo();
  }

  // 标签切换
  handleTabChange = (activeKey: string) => {
    this.filterInfoChange('dataType', activeKey);
  };

  // 筛选条件信息change
  filterInfoChange = (filterFieldName: string, value: any) => {
    const { filterInfoData, paginationData } = this.state;
    let newFilterInfoData = {} as any;
    const newPaginationData = {
      pageNum: 1,
      pageSize: paginationData.pageSize,
    };
    // 时间范围
    if (filterFieldName === 'timeRange') {
      newFilterInfoData = {
        ...filterInfoData,
        startTime: value?.[0],
        endTime: value?.[1],
      };
    } else {
      let newValue = value;
      // 反馈标签/反馈渠道/场景覆盖/涉及Agent
      // 将vlue二维数组展开平铺成一维数组
      if (_.includes(['feedbackTags', 'feedbackChannels', 'sceneCoverages', 'agents', 'isAnalysis', 'tgType'], filterFieldName)) {
        newValue = _.flatten(value);
      }
      newFilterInfoData = {
        ...filterInfoData,
        [filterFieldName]: newValue,
      };
      // 标签切换时，重新查询数据
      if (filterFieldName === 'dataType') {
        // 如果是【点赞】tab，则【反馈标签】(feedbackTags)字段值设为undefined
        if (newValue === 'likes') {
          newFilterInfoData = {
            ...filterInfoData,
            dataType: newValue,
            feedbackTags: undefined,
          };
        } else {
          newFilterInfoData = {
            ...filterInfoData,
            dataType: newValue,
          };
        }
        const persistenceObj = {
          filterParams: { ...filterInfoData, feedbackTags: undefined },
          pageParams: newPaginationData,
        };
        window.sessionStorage.setItem(persistenceKey,  JSON.stringify(persistenceObj));
        this.setState({ queryFilterInfoData: newFilterInfoData });
        // 查询数据反馈列表并重置到第一页
        this.queryDataFeedbackListInfo(newFilterInfoData, newPaginationData);
      }
    }
    // 更新筛选条件并重置到第一页
    this.setState({
      filterInfoData: newFilterInfoData,
      paginationData: newPaginationData,
    });
  };

  // 查询
  onQueryData = () => {
    const persistenceObj = {
      filterParams: { ...this.state.filterInfoData },
      pageParams: {
        page: 1,
        pageSize: this.state.paginationData.pageSize,
      },
    };
    console.log('点击查询存入的', this.state.filterInfoData, persistenceObj);
    window.sessionStorage.setItem(persistenceKey,  JSON.stringify(persistenceObj));
    this.setState({ queryFilterInfoData: this.state.filterInfoData });
    // 查询数据反馈列表
    this.queryDataFeedbackListInfo(this.state.filterInfoData);
  };

  // 点击traceID，跳转到可观测平台
  onTraceId = (traceId: string) => {
    const langfuseUrl = window.location.host === 'eip.htsc.com.cn' ? 'http://*************:3000/project/cmb9b22e70006qx070an0w0zc' : 'http://************:3000/project/cmcsoxnjp000eo508beut78ic';
    window.open(`${langfuseUrl}/traces?peek=${traceId}`);
  };

  // 查看会话详情
  onDetail = (conversationId: string, empId: string) => {
    const host = window.location.origin;
    // 环境上和本地打开地址不一样
    if(host.includes('localhost')) {
      // eslint-disable-next-line max-len
      window.open(`${host}/#/${BaseSettings.appName}/conversationDetail?empId=${empId}&conversationId=${conversationId}&breadcrumbName=数据反馈`);
    } else {
      // eslint-disable-next-line max-len
      window.open(`${host}/fspa/aorta-ai-platform/index.html#/${BaseSettings.appName}/conversationDetail?empId=${empId}&conversationId=${conversationId}&breadcrumbName=数据反馈`);
    }
  };

  // 分页、排序、筛选变化时触发
  onTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: SorterResult<DataFeedbackListItem>) => {
    const { paginationData, sortData } = this.state;
    const newPaginationData = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const newSortData = {
      sortField: sorter.field as string,
      sortOrder: sorter.order,
    };
    // 处理分页变化
    if (pagination.current !== paginationData.pageNum ||
      pagination.pageSize !== paginationData.pageSize) {
      this.setState({ paginationData: newPaginationData });
    }
    // 处理排序变化
    if (sorter.field !== sortData.sortField || sorter.order !== sortData.sortOrder) {
      this.setState({ sortData: newSortData });
    }
    const persistenceObj = {
      filterParams: this.state.queryFilterInfoData,
      pageParams:newPaginationData,
    };
    console.log('分页变化存入session', persistenceObj);
    window.sessionStorage.setItem(persistenceKey,  JSON.stringify(persistenceObj));
    // 查询数据反馈列表
    this.queryDataFeedbackListInfo({}, newPaginationData, newSortData);
  };

  render() {
    const {
      feedbackPerson,
      department,
      dictData,
      dataFeedbackList,
      queryDataFeedbackListLoading,
      queryFeedbackPersonLoading,
      queryFeedbackPerson,
    } = this.props;
    const { filterInfoData } = this.state;
    const { dataType } = filterInfoData;

    const items = [
      { label: '点踩', key: 'dislikes' },
      { label: '点赞', key: 'likes' },
    ];
    return (
      <div className={styles.dataFeedback}>
        {/* 顶部标题 */}
        <TopTitleInfo title="数据反馈" />
        <Tabs
          items={items}
          onChange={this.handleTabChange}
          className={styles.tabsStyle}
        />
        {/* 筛选条件信息 */}
        <FilterInfo
          dataType={dataType}
          feedbackPerson={feedbackPerson}
          department={department}
          dictData={dictData}
          queryFeedbackPersonLoading={queryFeedbackPersonLoading}
          queryFeedbackPerson={queryFeedbackPerson}
          filterInfoChange={this.filterInfoChange}
          handleQueryData={this.onQueryData}
        />
        {/* 列表 */}
        <TableList
          dataType={dataType}
          dataFeedbackList={dataFeedbackList}
          loading={queryDataFeedbackListLoading}
          handleTraceId={this.onTraceId}
          handleDetail={this.onDetail}
          handleTableChange={this.onTableChange}
        />
      </div>
    );
  }
}

// 初始化面包屑
const breadcrumbs = [{ name: '问TA管理平台' }, { name: '数据反馈' }];
// @ts-expect-error
export default WithCustomBreadcrumb(breadcrumbs)(DataFeedback);
