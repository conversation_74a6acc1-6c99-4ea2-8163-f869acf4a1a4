/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录
*/
import type { ConnectState } from '@/models/connect';
import type { TablePaginationConfig } from '@ht/sprite-ui/es/table';
import type { ConversationListItem } from '@/services/types/api/conversationRecord/queryConversationList';
import type { QuestionListItem } from '@/services/types/api/conversationRecord/queryQuestionList';
import type { FilterValue, SorterResult } from '@ht/sprite-ui/lib/table/interface';
import type { ConversationRecordProps, QuestionFilterInfoDataType, SubQuestionFilterInfoDataType, ConversationRecordState, PaginationDataType, SortDataType, ConversationFilterInfoDataType } from './data';

import React, { PureComponent } from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';
import { message, Tabs } from '@ht/sprite-ui';
import BaseSettings from '@config/BaseSettings';

import { generateEffect } from '@/utils/dvaHelper';
import WithCustomBreadcrumb from '@/components/common/WithCustomBreadcrumb';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import ConversationTabContent from '@/components/ConversationRecord/ConversationTabContent';
import QuestionTabContent from '@/components/ConversationRecord/QuestionTabContent';
import SubQuestionTabContent from '@/components/ConversationRecord/SubQuestionTabContent';
import { exportFileByPost } from '@/utils/httpBlob';

import styles from './index.less';
import { conversationRecordPersistenceKey } from './config';

const mapStateToProps = ({ conversationRecord, loading }: ConnectState) => ({
  /** 会话维度提问人 */
  conversationQuestioner: conversationRecord.conversationQuestioner,
  /** 问题维度提问人 */
  questionQuestioner: conversationRecord.questionQuestioner,
  /** 所属部门 */
  department: conversationRecord.department,
  /** 反馈标签和Agent字典 */
  dictData: conversationRecord.dictData,
  /** 答案来源字典 */
  dictAnsourceData: conversationRecord.dictAnsourceData,
  /** 会话维度列表 */
  conversationList: conversationRecord.conversationList,
  /** 会话维度的统计信息 */
  conversationStatics: conversationRecord.conversationStatics,
  /** 问题维度列表 */
  questionList: conversationRecord.questionList,
  /** 问题维度的统计信息 */
  questionStatics: conversationRecord.questionStatics,
  // 查询会话维度列表loading
  queryConversationListLoading: loading.effects['conversationRecord/queryConversationList'],
  // 查询问题维度列表loading
  queryQuestionListLoading: loading.effects['conversationRecord/queryQuestionList'],
  // 查询会话维度提问人loading
  queryConversationQuestionerLoading: loading.effects['conversationRecord/queryConversationQuestioner'],
  // 查询问题维度提问人loading
  queryQuestionQuestionerLoading: loading.effects['conversationRecord/queryQuestionQuestioner'],
  /** 子问题维度列表 */
  subQuestionList: conversationRecord.subQuestionList,
  /** 问题维度的统计信息 */
  subQuestionStatics: conversationRecord.subQuestionStatics,
  // 查询子问题维度列表loading
  querySubQuestionListLoading: loading.effects['conversationRecord/querySubQuestionList'],
});

const mapDispatchToProps = {
  /** 查询会话维度提问人 */
  queryConversationQuestioner: generateEffect('conversationRecord/queryConversationQuestioner'),
  /** 查询问题维度提问人 */
  queryQuestionQuestioner: generateEffect('conversationRecord/queryQuestionQuestioner'),
  /** 查询所属部门 */
  queryDepartmentInfo: generateEffect('conversationRecord/queryDepartmentInfo'),
  /** 查询反馈标签和Agent字典 */
  queryFeedbackTagsAndAgentDict: generateEffect('conversationRecord/queryFeedbackTagsAndAgentDict'),
  /** 查询答案来源字典 */
  queryAnswersSourcesDict: generateEffect('conversationRecord/queryAnswersSourcesDict'),
  /** 查询会话维度列表 */
  queryConversationList: generateEffect('conversationRecord/queryConversationList'),
  /** 查询会话维度的统计信息 */
  queryConversationStatics: generateEffect('conversationRecord/queryConversationStatics'),
  /** 查询问题维度列表 */
  queryQuestionList: generateEffect('conversationRecord/queryQuestionList'),
  /** 查询问题维度的统计信息 */
  queryQuestionStatics: generateEffect('conversationRecord/queryQuestionStatics'),
  /** 查询子问题维度列表 */
  querySubQuestionList: generateEffect('conversationRecord/querySubQuestionList'),
  /** 查询子问题维度的统计信息 */
  querySubQuestionStatics: generateEffect('conversationRecord/querySubQuestionStatics'),
};

// @ts-expect-error
@connect(mapStateToProps, mapDispatchToProps)
class ConversationRecord extends PureComponent<ConversationRecordProps, ConversationRecordState> {
  constructor(props: ConversationRecordProps) {
    super(props);
    this.state = {
      // 会话列表筛选条件数据-用于存放实时改变的筛选值
      conversationFilterInfoData: {
        // 首次提问开始时间
        startTime: '',
        // 首次提问结束时间
        endTime: '',
        // 反馈人
        empId: '',
        // 所属部门
        orgIdList: [],
        // 所属部门集合
        orgList: [],
        // 员工类型
        tgType: [],
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.conversationKey)!)?.filterParams,
      },
      // 会话列表筛选条件数据-用于真正下发接口的筛选值
      queryConversationFilterInfoData: {
        // 首次提问开始时间
        startTime: '',
        // 首次提问结束时间
        endTime: '',
        // 反馈人
        empId: '',
        // 所属部门
        orgIdList: [],
        // 所属部门集合
        orgList: [],
        // 员工类型
        tgType: [],
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.conversationKey)!)?.filterParams,
      },
      // 问题列表筛选条件数据-用于存放实时改变的筛选值
      questionFilterInfoData: {
        // 首次提问开始时间
        startTime: '',
        // 首次提问结束时间
        endTime: '',
        // 反馈人
        empId: '',
        // 问题搜索
        keyWord: '',
        // 所属部门
        orgIdList: [],
        // 所属部门集合
        orgList: [],
        // 答案反馈
        answerFeedback: [],
        // 场景覆盖
        sceneCoverages: [],
        // 涉及Agent
        agents: [],
        // 员工类型
        tgType: [],
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.questionKey)!)?.filterParams,
      },
      // 问题列表查询用的筛选条件数据-用于真正下发接口的筛选值
      queryQuestionFilterInfoData: {
        // 首次提问开始时间
        startTime: '',
        // 首次提问结束时间
        endTime: '',
        // 反馈人
        empId: '',
        // 问题搜索
        keyWord: '',
        // 所属部门
        orgIdList: [],
        // 所属部门集合
        orgList: [],
        // 答案反馈
        answerFeedback: [],
        // 场景覆盖
        sceneCoverages: [],
        // 涉及Agent
        agents: [],
        // 员工类型
        tgType: [],
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.questionKey)!)?.filterParams,
      },
      // 会话列表分页数据
      conversationListpaginationData: {
        pageNum: 1,
        pageSize: 20,
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.conversationKey)!)?.pageParams,
      },
      // 问题列表分页数据
      questionListpaginationData: {
        pageNum: 1,
        pageSize: 20,
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.questionKey)!)?.pageParams,
      },
      // 子问题列表分页数据
      subQuestionListpaginationData: {
        pageNum: 1,
        pageSize: 20,
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.subQuestionKey)!)?.pageParams,
      },
      // 会话列表排序数据
      conversationListsortData: {
        // 排序字段
        sortField: 'questionTime',
        // 排序顺序
        sortOrder: 'desc',
      },
      // 问题列表排序数据
      questionListsortData: {
        // 排序字段
        sortField: 'questionTime',
        // 排序顺序
        sortOrder: 'desc',
      },
      // 问题列表排序数据
      subQuestionListsortData: {
        // 排序字段
        sortField: 'questionTime',
        // 排序顺序
        sortOrder: 'desc',
      },
      // 子问题列表查询用的筛选条件数据-用于真正下发接口的筛选值
      querySubQuestionFilterInfoData: {
        // 首次提问开始时间
        startTime: '',
        // 首次提问结束时间
        endTime: '',
        // 提问人
        empId: '',
        // 子问题搜索
        keyword:'',
        // 答案来源
        answersSources: [],
        // 场景覆盖
        sceneCoverages: [],
        // 涉及Agent
        agents: [],
        // 员工类型
        tgType: [],
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.subQuestionKey)!)?.filterParams,
      },
      // 子问题列表筛选条件数据-用于存放实时改变的筛选值
      subQuestionFilterInfoData: {
        // 首次提问开始时间
        startTime: '',
        // 首次提问结束时间
        endTime: '',
        // 提问人
        empId: '',
        // 子问题搜索
        keyword:'',
        // 答案来源
        answersSources: [],
        // 场景覆盖
        sceneCoverages: [],
        // 涉及Agent
        agents: [],
        // 员工类型
        tgType: [],
        ...JSON.parse(window.sessionStorage.getItem(conversationRecordPersistenceKey.subQuestionKey)!)?.filterParams,
      },
      // 子问题下载按钮加载状态
      subLoading:false,
    };

  }

  // 查询会话维度列表
  queryConversationListInfo = (
    newConversationFilterInfoData?: Partial<ConversationFilterInfoDataType>,
    newPaginationData?: Partial<PaginationDataType>,
    newSortData?: Partial<SortDataType>,
  ) => {
    const { queryConversationList } = this.props;
    const { queryConversationFilterInfoData, conversationListpaginationData, conversationListsortData } = this.state;
    // 最终的排序数据
    const finalSortData = {
      ...conversationListsortData,
      ...newSortData,
    };
    // 处理排序字段
    if (finalSortData.sortOrder) {
      finalSortData.sortOrder = finalSortData.sortOrder === 'ascend' ? 'asc' : 'desc';
    }
    console.log('会话维度查询', queryConversationFilterInfoData);
    const { orgList } = { ...queryConversationFilterInfoData, ...newConversationFilterInfoData };
    const queryOrgIdList: any[] = [];
    orgList.forEach((item: { value: any; }) => {
      if (item.value !== '不限') {
        queryOrgIdList.push(item.value);
      }
    });
    // 合并请求入参
    const searchFilterInfoData = {
      ...queryConversationFilterInfoData,
      ...newConversationFilterInfoData,
      orgIdList:  queryOrgIdList,
      ...conversationListpaginationData,
      ...newPaginationData,
      ...finalSortData,
    };
    queryConversationList(searchFilterInfoData);
  };

  // 查询会话维度的统计信息
  queryConversationStaticsInfo = () => {
    const { queryConversationStatics } = this.props;
    const { conversationFilterInfoData } = this.state;
    const { orgList } = conversationFilterInfoData;
    const queryOrgIdList: string[] = [];
    orgList?.forEach((item: { value: any; }) => {
      if (item.value !== '不限') {
        queryOrgIdList.push(item.value);
      }
    });
    queryConversationStatics({
      ...conversationFilterInfoData,
      orgIdList: queryOrgIdList ?? [],
    });
  }

  // 查询问题维度列表
  queryQuestionListInfo = (
    newQeustionFilterInfoData?: Partial<QuestionFilterInfoDataType>,
    newPaginationData?: Partial<PaginationDataType>,
    newSortData?: Partial<SortDataType>,
  ) => {
    const { queryQuestionList } = this.props;
    const { queryQuestionFilterInfoData, questionListpaginationData, questionListsortData } = this.state;
    // 最终的排序数据
    const finalSortData = {
      ...questionListsortData,
      ...newSortData,
    };
    // 处理排序字段
    if (finalSortData.sortOrder) {
      finalSortData.sortOrder = finalSortData.sortOrder === 'ascend' ? 'asc' : 'desc';
    }
    const { orgList } = { ...queryQuestionFilterInfoData, ...newQeustionFilterInfoData };
    const queryOrgIdList: any[] = [];
    orgList.forEach((item: { value: any; }) => {
      if (item.value !== '不限') {
        queryOrgIdList.push(item.value);
      }
    });
    // 合并请求入参
    const searchFilterInfoData = {
      ...queryQuestionFilterInfoData,
      ...newQeustionFilterInfoData,
      orgIdList:  queryOrgIdList,
      ...questionListpaginationData,
      ...newPaginationData,
      ...finalSortData,
    };
    queryQuestionList(searchFilterInfoData);
  };

  // 查询问题维度的统计信息
  queryQuestionStaticsInfo = () => {
    const { queryQuestionStatics } = this.props;
    const { questionFilterInfoData } = this.state;
    const { orgList } = questionFilterInfoData;
    const queryOrgIdList: string[] = [];
    orgList?.forEach((item: { value: any; }) => {
      if (item.value !== '不限') {
        queryOrgIdList.push(item.value);
      }
    });
    queryQuestionStatics({
      ...questionFilterInfoData,
      orgIdList:  queryOrgIdList ?? [],
    });
  }

  // 查询子问题维度的统计信息
  querySubQuestionStaticsInfo = () => {
    const { querySubQuestionStatics } = this.props;
    const { subQuestionFilterInfoData } = this.state;
    querySubQuestionStatics(subQuestionFilterInfoData);
  }

  // 查询子问题问题维度列表
  querySubQuestionListInfo = (
    newQeustionFilterInfoData?: Partial<QuestionFilterInfoDataType>,
    newPaginationData?: Partial<PaginationDataType>,
    newSortData?: Partial<SortDataType>,
  ) => {
    const { querySubQuestionList } = this.props;
    const { querySubQuestionFilterInfoData, subQuestionListpaginationData, subQuestionListsortData } = this.state;
    // 最终的排序数据
    const finalSortData = {
      ...subQuestionListsortData,
      ...newSortData,
    };
    // 处理排序字段
    if (finalSortData.sortOrder) {
      finalSortData.sortOrder = finalSortData.sortOrder === 'ascend' ? 'asc' : 'desc';
    }

    // 合并请求入参
    const searchFilterInfoData = {
      ...querySubQuestionFilterInfoData,
      ...newQeustionFilterInfoData,
      ...subQuestionListpaginationData,
      ...newPaginationData,
      ...finalSortData,
    };
    querySubQuestionList(searchFilterInfoData);
  };

  componentDidMount() {
    const { queryDepartmentInfo, queryFeedbackTagsAndAgentDict, queryAnswersSourcesDict } = this.props;
    // 查询所属部门
    // 投顾发展部(ZZ323372) 平台运营部(ZZ500107) 财富管理部(ZZ001041) 信息技术部(ZZ001014) 融资融券部(ZZ001112) 金融产品部(zz001128)
    queryDepartmentInfo({ hqOrgCodeList: ['ZZ323372', 'ZZ500107', 'ZZ001041', 'ZZ001014', 'ZZ001112', 'zz001128'] });
    // 查询反馈标签和Agent字典
    queryFeedbackTagsAndAgentDict();
    // 查询答案来源字典
    queryAnswersSourcesDict();
    // 查询会话维度列表
    this.queryConversationListInfo(this.state.queryConversationFilterInfoData);
    // 查询会话维度的统计信息
    this.queryConversationStaticsInfo();
  }

  // 标签切换/查询
  handleTabChange = (activeKey: string) => {
    if (activeKey === 'conversation') {
      const { conversationFilterInfoData, conversationListpaginationData } = this.state;
      this.setState({ queryConversationFilterInfoData: conversationFilterInfoData });
      const newPageData = {
        pageNum: 1,
        pageSize: conversationListpaginationData.pageSize,
      };
      // 查询会话维度列表
      this.queryConversationListInfo(conversationFilterInfoData, newPageData);
      // 查询会话维度的统计信息
      this.queryConversationStaticsInfo();
      const searchFilterInfoData = {
        filterParams: conversationFilterInfoData,
        pageParams: newPageData,
      };
      console.log('会话维度查询存入session', searchFilterInfoData);
      window.sessionStorage.setItem(conversationRecordPersistenceKey.conversationKey, JSON.stringify(searchFilterInfoData));
    } else if(activeKey === 'question') {
      // 查询问题维度的统计信息
      this.queryQuestionStaticsInfo();
      // 查询问题维度列表
      const { questionFilterInfoData, questionListpaginationData } = this.state;
      this.setState({ queryQuestionFilterInfoData: questionFilterInfoData });
      const newPageData = {
        pageNum: 1,
        pageSize: questionListpaginationData.pageSize,
      };
      this.queryQuestionListInfo(questionFilterInfoData, newPageData);
      const searchFilterInfoData = {
        filterParams: questionFilterInfoData,
        pageParams: newPageData,
      };
      console.log('问题维度查询存入session', searchFilterInfoData);
      window.sessionStorage.setItem(conversationRecordPersistenceKey.questionKey, JSON.stringify(searchFilterInfoData));
      this.setState({ queryQuestionFilterInfoData:questionFilterInfoData });
    } else {

      // 查询子问题维度列表
      const { subQuestionFilterInfoData, subQuestionListpaginationData } = this.state;
      this.setState({ querySubQuestionFilterInfoData: subQuestionFilterInfoData });
      const newPageData = {
        pageNum: 1,
        pageSize: subQuestionListpaginationData.pageSize,
      };
      this.querySubQuestionListInfo(subQuestionFilterInfoData, newPageData);
      const searchFilterInfoData = {
        filterParams: subQuestionFilterInfoData,
        pageParams: newPageData,
      };
      console.log('问题维度查询存入session', searchFilterInfoData);
      window.sessionStorage.setItem(conversationRecordPersistenceKey.subQuestionKey, JSON.stringify(searchFilterInfoData));
      this.setState({ querySubQuestionFilterInfoData: subQuestionFilterInfoData });
      // 查询子问题维度的统计信息
      this.querySubQuestionStaticsInfo();
    }
  };

  // 会话列表筛选条件信息change
  conversationFilterInfoChange = (filterFieldName: string, value: any) => {
    const { conversationFilterInfoData, conversationListpaginationData } = this.state;
    let newFilterInfoData = {} as any;
    // 首次提问时间
    if (filterFieldName === 'timeRange') {
      newFilterInfoData = {
        ...conversationFilterInfoData,
        startTime: value?.[0],
        endTime: value?.[1],
      };
    } else {
      let newValue = value;
      // 答案反馈
      // 将vlue二维数组展开平铺成一维数组
      if (_.includes([ 'tgType'], filterFieldName)) {
        newValue = _.flatten(value);
      }
      newFilterInfoData = {
        ...conversationFilterInfoData,
        [filterFieldName]: newValue,
      };
    }
    // 更新筛选条件并重置到第一页
    const newPaginationData = {
      pageNum: 1,
      pageSize: conversationListpaginationData.pageSize,
    };
    this.setState({
      conversationFilterInfoData: newFilterInfoData,
      conversationListpaginationData: newPaginationData,
    });
  };

  // 问题列表筛选条件信息change
  questionFilterInfoChange = (filterFieldName: string, value: any) => {
    const { questionFilterInfoData, questionListpaginationData } = this.state;
    let newFilterInfoData = {} as any;
    // 提问时间
    if (filterFieldName === 'timeRange') {
      newFilterInfoData = {
        ...questionFilterInfoData,
        startTime: value?.[0],
        endTime: value?.[1],
      };
    } else {
      let newValue = value;
      // 答案反馈
      // 将vlue二维数组展开平铺成一维数组
      if (_.includes([ 'answerFeedback', 'sceneCoverages', 'agents', 'tgType'], filterFieldName)) {
        newValue = _.flatten(value);
      }
      newFilterInfoData = {
        ...questionFilterInfoData,
        [filterFieldName]: newValue,
      };
    }
    // 更新筛选条件并重置到第一页
    const newPaginationData = {
      pageNum: 1,
      pageSize: questionListpaginationData.pageSize,
    };
    this.setState({
      questionFilterInfoData: newFilterInfoData,
      questionListpaginationData: newPaginationData,
    });
  };

  // 子问题列表筛选条件信息change
  subQuestionFilterInfoChange = (filterFieldName: string, value: any) => {
    const { subQuestionFilterInfoData, subQuestionListpaginationData } = this.state;
    let newFilterInfoData = {} as any;
    // 提问时间
    if (filterFieldName === 'timeRange') {
      newFilterInfoData = {
        ...subQuestionFilterInfoData,
        startTime: value?.[0],
        endTime: value?.[1],
      };
    } else {
      let newValue = value;
      // 答案反馈
      // 将vlue二维数组展开平铺成一维数组
      if (_.includes([ 'answersSources', 'sceneCoverages', 'agents', 'tgType'], filterFieldName)) {
        newValue = _.flatten(value);
      }
      newFilterInfoData = {
        ...subQuestionFilterInfoData,
        [filterFieldName]: newValue,
      };
    }
    // 更新筛选条件并重置到第一页
    const newPaginationData = {
      pageNum: 1,
      pageSize: subQuestionListpaginationData.pageSize,
    };
    console.log('newFilterInfoData', newFilterInfoData);
    this.setState({
      subQuestionFilterInfoData: newFilterInfoData,
      subQuestionListpaginationData: newPaginationData,
    });
  };

  // 点击traceID，跳转到可观测平台
  onTraceId = (traceId: string) => {
    const langfuseUrl = window.location.host === 'eip.htsc.com.cn' ? 'http://*************:3000/project/cmb9b22e70006qx070an0w0zc' : 'http://************:3000/project/cmcsoxnjp000eo508beut78ic';
    window.open(`${langfuseUrl}/traces?peek=${traceId}`);
  };

  // 查看会话详情
  onDetail = (conversationId: string, empId: string) => {
    const host = window.location.origin;
    // 环境上和本地打开地址不一样
    if(host.includes('localhost')) {
      // eslint-disable-next-line max-len
      window.open(`${host}/#/${BaseSettings.appName}/conversationDetail?empId=${empId}&conversationId=${conversationId}&breadcrumbName=会话记录`);
    } else {
      // eslint-disable-next-line max-len
      window.open(`${host}/fspa/aorta-ai-platform/index.html#/${BaseSettings.appName}/conversationDetail?empId=${empId}&conversationId=${conversationId}&breadcrumbName=会话记录`);
    }
  };

  // 会话列表的分页、排序、筛选变化时触发
  conversationTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: SorterResult<ConversationListItem>) => {
    const { conversationListpaginationData, conversationListsortData } = this.state;
    const newPaginationData = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const newSortData = {
      sortField: sorter.field as string,
      sortOrder: sorter.order,
    };
    // 处理分页变化
    if (pagination.current !== conversationListpaginationData.pageNum ||
      pagination.pageSize !== conversationListpaginationData.pageSize) {
      this.setState({ conversationListpaginationData: newPaginationData });
    }
    // 处理排序变化
    if (sorter.field !== conversationListsortData.sortField || sorter.order !== conversationListsortData.sortOrder) {
      this.setState({ conversationListsortData: newSortData });
    }
    const persistenceObj = {
      filterParams: this.state.queryConversationFilterInfoData,
      pageParams:newPaginationData,
    };
    window.sessionStorage.setItem(conversationRecordPersistenceKey.conversationKey,  JSON.stringify(persistenceObj));
    // 查询会话维度列表
    this.queryConversationListInfo({}, newPaginationData, newSortData);
  };

  // 问题列表的分页、排序、筛选变化时触发
  questionTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: SorterResult<QuestionListItem>) => {
    const { questionListpaginationData, questionListsortData } = this.state;
    const newPaginationData = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const newSortData = {
      sortField: sorter.field as string,
      sortOrder: sorter.order,
    };
    // 处理分页变化
    if (pagination.current !== questionListpaginationData.pageNum ||
      pagination.pageSize !== questionListpaginationData.pageSize) {
      this.setState({ questionListpaginationData: newPaginationData });
    }
    // 处理排序变化
    if (sorter.field !== questionListsortData.sortField || sorter.order !== questionListsortData.sortOrder) {
      this.setState({ questionListsortData: newSortData });
    }
    const persistenceObj = {
      filterParams: this.state.queryQuestionFilterInfoData,
      pageParams:newPaginationData,
    };
    window.sessionStorage.setItem(conversationRecordPersistenceKey.questionKey,  JSON.stringify(persistenceObj));
    // 查询问题维度列表
    this.queryQuestionListInfo({}, newPaginationData, newSortData);
  };

  // 问题列表的分页、排序、筛选变化时触发
  subQuestionTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: SorterResult<QuestionListItem>) => {
    const { subQuestionListpaginationData, subQuestionListsortData } = this.state;
    const newPaginationData = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const newSortData = {
      sortField: sorter.field as string,
      sortOrder: sorter.order,
    };
    // 处理分页变化
    if (pagination.current !== subQuestionListpaginationData.pageNum ||
      pagination.pageSize !== subQuestionListpaginationData.pageSize) {
      this.setState({ subQuestionListpaginationData: newPaginationData });
    }
    // 处理排序变化
    if (sorter.field !== subQuestionListsortData.sortField || sorter.order !== subQuestionListsortData.sortOrder) {
      this.setState({ subQuestionListsortData: newSortData });
    }
    const persistenceObj = {
      filterParams: this.state.querySubQuestionFilterInfoData,
      pageParams:newPaginationData,
    };
    window.sessionStorage.setItem(conversationRecordPersistenceKey.subQuestionKey,  JSON.stringify(persistenceObj));
    // 查询问题维度列表
    this.querySubQuestionListInfo({}, newPaginationData, newSortData);
  };

  // 下载问题维度列表
  downloadQuestionList = () => {
    const { questionFilterInfoData } = this.state;
    const { orgList } = questionFilterInfoData;
    const queryOrgIdList: any[] = [];
    orgList.forEach((item: { value: any; }) => {
      if (item.value !== '不限') {
        queryOrgIdList.push(item.value);
      }
    });
    exportFileByPost({
      url: '/fspa/aorta/ai/api/desktop/file/exportQuestionExcel',
      params: { ...questionFilterInfoData, orgIdList: queryOrgIdList },
      // fileName: '问题维度列表.xlsx',
    }).then(
      (res: any) => {
        if (res instanceof Blob) {
          message.success('导出成功');
        } else {
          message.error(res?.msg || '导出失败');
        }
      },
    );
  };

  // 下载子问题维度列表
  downloadSubQuestionList = () => {
    const { subQuestionFilterInfoData } = this.state;
    this.setState({
      subLoading:true,
    });
    exportFileByPost({
      url: '/fspa/aorta/ai/api/desktop/file/exportSubQuestionExcel',
      params: subQuestionFilterInfoData,
    }).then(
      (res: any) => {
        this.setState({
          subLoading:false,
        });
        if (res instanceof Blob) {
          message.success('导出成功');
        } else {
          message.error(res?.msg || '导出失败');
        }
      },
    );
  };

   handleQuestionTabQuery = () => {
     this.handleTabChange('conversation');
     const { questionFilterInfoData, questionListpaginationData } = this.state;
     const searchFilterInfoData = {
       filterParams: questionFilterInfoData,
       pageParams: questionListpaginationData,
     };
     console.log('问题维度查询存入session', searchFilterInfoData);
     window.sessionStorage.setItem(conversationRecordPersistenceKey.questionKey, JSON.stringify(searchFilterInfoData));
   };

   render() {
     const {
       conversationQuestioner,
       questionQuestioner,
       department,
       dictData,
       conversationList,
       conversationStatics,
       questionList,
       questionStatics,
       queryConversationListLoading,
       queryQuestionListLoading,
       queryConversationQuestionerLoading,
       queryQuestionQuestionerLoading,
       queryConversationQuestioner,
       queryQuestionQuestioner,
       subQuestionList,
       subQuestionStatics,
       querySubQuestionListLoading,
       dictAnsourceData,
     } = this.props;
     // 会话Tab内容props
     const conversationTabContentProps = {
       conversationQuestioner,
       department,
       dictData,
       conversationList,
       conversationStatics,
       queryConversationListLoading,
       queryConversationQuestionerLoading,
       queryConversationQuestioner,
       filterInfoChange: this.conversationFilterInfoChange,
       handleQueryData: () => this.handleTabChange('conversation'),
       handleDetail: this.onDetail,
       handleTableChange: this.conversationTableChange,
     };
     const questionTabContentProps = {
       questionQuestioner,
       department,
       dictData,
       questionList,
       questionStatics,
       queryQuestionListLoading,
       queryQuestionQuestionerLoading,
       queryQuestionQuestioner,
       filterInfoChange: this.questionFilterInfoChange,
       handleQueryData: () => this.handleTabChange('question'),
       //  handleQueryData: this.handleQuestionTabQuery,
       handleDownloadData: this.downloadQuestionList,
       handleTraceId: this.onTraceId,
       handleDetail: this.onDetail,
       handleTableChange: this.questionTableChange,
     };

     const subQuestionTabContentProps = {
       questionQuestioner,
       department,
       dictData,
       dictAnsourceData,
       subQuestionList,
       subQuestionStatics,
       querySubQuestionListLoading,
       queryQuestionQuestionerLoading,
       queryQuestionQuestioner,
       filterInfoChange: this.subQuestionFilterInfoChange,
       handleQueryData: () => this.handleTabChange('subproblem'),
       handleDownloadData: this.downloadSubQuestionList,
       handleTraceId: this.onTraceId,
       handleDetail: this.onDetail,
       handleTableChange: this.subQuestionTableChange,
       subLoading:this.state.subLoading,
     };
     const items = [
       {
         label: '会话维度',
         key: 'conversation',
         children: <ConversationTabContent { ...conversationTabContentProps } />,
       },
       {
         label: '问题维度',
         key: 'question',
         children: <QuestionTabContent { ...questionTabContentProps } />,
       },
       {
         label: '子问题维度',
         key: 'subproblem',
         children: <SubQuestionTabContent { ...subQuestionTabContentProps } />,
       },
     ];
     return (
       <div className={styles.conversationRecord}>
         {/* 顶部标题 */}
         <TopTitleInfo title="会话记录" />
         <Tabs
           items={items}
           onChange={this.handleTabChange}
           className={styles.tabsStyle}
         />
       </div>
     );
   }
}

// 初始化面包屑
const breadcrumbs = [{ name: '问TA管理平台' }, { name: '会话记录' }];
// @ts-expect-error
export default WithCustomBreadcrumb(breadcrumbs)(ConversationRecord);
