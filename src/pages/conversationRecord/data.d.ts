import type { Dispatch, History } from '@oula/oula';
import type {
  FeedbackPersonResult,
  IQueryFeedbackPerson,
} from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type {
  DepartmentInfoItem,
  DictResult,
  IQueryDepartmentInfo,
} from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type {
  ConversationList,
  IQueryConversationList,
} from '@/services/types/api/conversationRecord/queryConversationList';
import type {
  ConversationStaticsResult,
  IQueryConversationStatics,
} from '@/services/types/api/conversationRecord/queryConversationStatics';
import type {
  QuestionList,
  IQueryQuestionList,
  IQuerySubQuestionList,
} from '@/services/types/api/conversationRecord/queryQuestionList';
import type {
  QuestionStaticsResult,
  IQueryQuestionStatics,
} from '@/services/types/api/conversationRecord/queryQuestionStatics';

export type ConversationFilterInfoDataType = {
  /** 首次提问开始时间 */
  startTime: string;
  /** 首次提问结束时间 */
  endTime: string;
  /** 提问人 */
  empId: string;
  /** 所属部门 */
  orgIdList: string[];
  orgList: any[];
}
export type QuestionFilterInfoDataType = ConversationFilterInfoDataType & {
  /** 答案反馈 */
  answerFeedback: string[];
}
export type SubQuestionFilterInfoDataType = ConversationFilterInfoDataType & {
  /** 答案反馈 */
  answersSources: string[];
}
export type PaginationDataType = {
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
}

export type SortDataType = {
  /** 排序字段 */
  sortField: string;
  /** 排序顺序 */
  sortOrder?: string | null;
}

export interface ConversationRecordProps {
  dispatch: Dispatch;
  history: History;
  /** 会话维度提问人 */
  conversationQuestioner: FeedbackPersonResult;
  /** 问题维度提问人 */
  questionQuestioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  /** 答案来源字典 */
  dictAnsourceData: DictResult;
  /** 会话维度列表 */
  conversationList: ConversationList;
  /** 会话维度的统计信息 */
  conversationStatics: ConversationStaticsResult,
  /** 问题维度列表 */
  questionList: QuestionList;
  /** 子问题维度列表 */
  subQuestionList: QuestionList;
  /** 问题维度的统计信息 */
  questionStatics: QuestionStaticsResult,
  /** 问题维度的统计信息 */
  subQuestionStatics: QuestionStaticsResult,
  /** 查询会话维度列表loading */
  queryConversationListLoading: boolean;
  /** 查询问题维度列表loading */
  queryQuestionListLoading: boolean;
  /** 查询子问题维度列表loading */
  querySubQuestionListLoading: boolean;
  /** 查询会话维度提问人loading */
  queryConversationQuestionerLoading: boolean;
  /** 查询问题维度提问人loading */
  queryQuestionQuestionerLoading: boolean;
  /** 查询会话维度提问人 */
  queryConversationQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  /** 查询问题维度提问人 */
  queryQuestionQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  /** 查询所属部门 */
  queryDepartmentInfo: (query: IQueryDepartmentInfo['Query']) => Promise<unknown>;
  /** 查询agent字典 */
  queryFeedbackTagsAndAgentDict: () => Promise<unknown>;
  /** 查询答案来源字典 */
  queryAnswersSourcesDict: () => Promise<unknown>;
  /** 查询会话维度列表 */
  queryConversationList: (query: IQueryConversationList['Query']) => Promise<unknown>;
  /** 查询会话维度的统计信息 */
  queryConversationStatics: (query: IQueryConversationStatics['Query']) => Promise<unknown>;
  /** 查询问题维度列表 */
  queryQuestionList: (query: IQueryQuestionList['Query']) => Promise<unknown>;
  /** 查询问题维度的统计信息 */
  queryQuestionStatics: (query: IQueryQuestionStatics['Query']) => Promise<unknown>;
  /** 查询子问题维度列表 */
  querySubQuestionList: (query: IQuerySubQuestionList['Query']) => Promise<unknown>;
  /** 查询问题维度的统计信息 */
  querySubQuestionStatics: (query: IQueryQuestionStatics['Query']) => Promise<unknown>;
}

export interface ConversationRecordState {
  /** 会话列表筛选条件数据 */
  conversationFilterInfoData: ConversationFilterInfoDataType;
  /** 会话列表筛选条件数据 */
  queryConversationFilterInfoData: ConversationFilterInfoDataType;
  /** 问题列表筛选条件数据 */
  questionFilterInfoData: QuestionFilterInfoDataType;
  /** 问题列表筛选条件数据 */
  queryQuestionFilterInfoData: QuestionFilterInfoDataType;
  /** 会话列表分页数据 */
  conversationListpaginationData: PaginationDataType;
  /** 问题列表分页数据 */
  questionListpaginationData: PaginationDataType;
  /** 子问题列表分页数据 */
  subQuestionListpaginationData: PaginationDataType;
  /** 会话列表排序数据 */
  conversationListsortData: SortDataType;
  /** 问题列表排序数据 */
  questionListsortData: SortDataType;
  /** 子问题列表排序数据 */
  subQuestionListsortData: SortDataType;
  /** 问题列表筛选条件数据 */
  querySubQuestionFilterInfoData: SubQuestionFilterInfoDataType;
  /** 子问题列表筛选条件数据 */
  subQuestionFilterInfoData: SubQuestionFilterInfoDataType;
  subLoading:boolean;
}
