import type { Dispatch } from '@oula/oula';
import type { ParsedQuery } from 'query-string';
import type { MenuDataItem } from '@umijs/route-utils';
import type { IUserInfo, ITenantListItm } from '@/services/types/api/login/queryEmpInfo';
import type { DataSourcesItem } from '@/services/types/api/fileManagement/queryDataSources';
import type {
  FileCategoryItem,
  IQueryFileCategory,
} from '@/services/types/api/fileManagement/queryFileCategory';
import type { IUpdateFileCategory } from '@/services/types/api/fileManagement/updateFileCategory';
import type { IUpdateFileStatus } from '@/services/types/api/fileManagement/updateFileStatus';
import type {
  FileManagementList,
  IQueryFileManagementList,
} from '@/services/types/api/fileManagement/queryFileManagementList';
import type {
  FileManagementModelState,
} from '@/models/fileManagement';
import type { PannelsName } from '@/components/FileAnalysis/config';

export type FilterInfoDataType = {
  /** 文件名称 */
  fileName: string;
  /** 类目 */
  categoryId: string[];
  /** 数据来源 */
  dataSourceId: string[];
  /** 切片是否校验 */
  chunkVerifyStatus: string[],
  /** 文件状态 */
  fileStatus: string[],
}

export type PaginationDataType = {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
}

export interface FileManagementProps {
  dispatch: Dispatch;
  /** 租户id */
  tenantId: string;
  /** 用户信息 */
  userInfo: IUserInfo;
  /** 数据来源 */
  dataSources: DataSourcesItem[];
  /** 文件类目 */
  fileCategory: FileCategoryItem[];
  /** 文件管理字典 */
  fileManageDict: any;
  /** 文件管理列表 */
  fileManagementList: FileManagementList;
  /** 文件管理列表loading */
  queryFileManagementListLoading: boolean;
  /** 查询文件管理列表 */
  queryFileManagementList: (query: IQueryFileManagementList['Query']) => Promise<unknown>;
  /** 查询文件类目 */
  queryFileCategory: (query: IQueryFileCategory['Query']) => Promise<unknown>;
  /** 查询数据来源 */
  queryDataSources: () => Promise<unknown>;
  /** 查询文件管理字典 */
  queryFileManageDict: () => Promise<unknown>;
  /** 单个或批量编辑类目 */
  updateFileCategory: (query: IUpdateFileCategory['Query']) => Promise<boolean>;
  /** 编辑是否启用 */
  updateFileStatus: (query: IUpdateFileStatus['Query']) => Promise<boolean>;
  /** 删除文件 */
  deleteFile: ({ fileId, tenantId }: { fileId: string; tenantId: string; }) => Promise<boolean>;
  /** 删除文件 */
  clearRedux: (data: Partial<FileManagementModelState>) => void;
  /** 设置面包屑 */
  setBreadcrumb: (breadcrumbList: MenuDataItem[]) => void;
}

export interface FileManagementState {
  /** 当前面板 */
  currentPannel: PannelsName[];
  /** 是否批量操作 */
  batchOperation: boolean;
  /** 列表选择功能的配置 */
  rowSelection: any;
  /** 批量操作相关按钮是否禁用 */
  batchOperationButDisabled: boolean;
  /** 文件批量上传面板 */
  fileUploadPanelVisible: boolean;
  /** 文件解析面板 */
  fileAnalysisPannelVisible: boolean;
  /** 文件解析切片面板 */
  fileAnalysisChunksPannelVisible: boolean;
  /** 文档召回测试面板 */
  fileRecallTestPannelVisible: boolean;
  /** 类目和文档的关联关系id（批量编辑类目入参用到） */
  relationIdList: string[];
  /** 文件解析面板、文件解析的切片面板需要用到的fileId列表 */
  fileIdList: string[];
  /** 类目弹框标题 */
  categoryModalTitle: string;
  /** 类目弹框是否可见 */
  categoryModalVisible: boolean;
  /** 类目弹框类型 */
  categoryModalType: string;
  /** 类目弹框所选的文件类目 */
  categoryValue: (string | number)[],
  /** 筛选条件数据 */
  filterInfoData: FilterInfoDataType;
  /** 分页数据 */
  paginationData: PaginationDataType;
  /** 当前location中的query  */
  currentLocationQuery: ParsedQuery<string| number> | undefined;
}
