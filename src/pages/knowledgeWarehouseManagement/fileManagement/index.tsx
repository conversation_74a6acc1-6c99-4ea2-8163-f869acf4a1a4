/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-10
 * @Description：知识库管理-文件管理
*/
import type { ConnectState } from '@/models/connect';
import type { MenuDataItem } from '@umijs/route-utils';
import type { PannelsName } from '@/components/FileAnalysis/config';
import type { FileManagementProps, FileManagementState, FilterInfoDataType, PaginationDataType } from './data';

import React, { PureComponent } from 'react';
import _ from 'lodash';
import { connect, history } from '@oula/oula';

import { generateEffect } from '@/utils/dvaHelper';
import WithCustomBreadcrumb from '@/components/common/WithCustomBreadcrumb';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import CategoryModal from '@/components/KnowledgeWarehouseManagement/FileManagement/CategoryModal';
import TopRightButton from '@/components/KnowledgeWarehouseManagement/FileManagement/TopRightButton';
import FilterInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/FilterInfo';
import FileTable from '@/components/KnowledgeWarehouseManagement/FileManagement/FileTable';
import FileAnalysisPannel from '@/components/FileAnalysis/FileAnalysisPannel';
import FileAnalysisChunksPannel from '@/components/FileAnalysis/FileAnalysisChunksPannel';
import FileRecallTestPannel from '@/components/FileAnalysis/FileRecallTestPannel';
import FileUploadPanel from '@/components/FileAnalysis/FileUploadPanel';
import { Pannels } from '@/components/FileAnalysis/config';

import styles from './index.less';

const { replace, location } = history;

const mapStateToProps = ({ global, fileManagement, loading }: ConnectState) => ({
  /** 租户id */
  tenantId: global.tenantId,
  /** 用户信息 */
  userInfo: global.userInfo,
  /** 文件类目 */
  fileCategory: fileManagement.fileCategory,
  /** 数据来源 */
  dataSources: fileManagement.dataSources,
  /** 文件管理字典 */
  fileManageDict: fileManagement.fileManageDict,
  /** 文件管理列表 */
  fileManagementList: fileManagement.fileManagementList,
  // 文件管理列表loading
  queryFileManagementListLoading: loading.effects['fileManagement/queryFileManagementList'],
});

const mapDispatchToProps = {
  /** 查询文件管理列表 */
  queryFileManagementList: generateEffect('fileManagement/queryFileManagementList'),
  /** 查询文件类目 */
  queryFileCategory: generateEffect('fileManagement/queryFileCategory'),
  /** 查询数据来源 */
  queryDataSources: generateEffect('fileManagement/queryDataSources'),
  /** 查询文件管理字典 */
  queryFileManageDict: generateEffect('fileManagement/queryFileManageDict'),
  /** 单个或批量编辑类目 */
  updateFileCategory: generateEffect('fileManagement/updateFileCategory'),
  /** 编辑是否启用 */
  updateFileStatus: generateEffect('fileManagement/updateFileStatus'),
  /** 删除文件 */
  deleteFile: generateEffect('fileManagement/deleteFile'),
  /** 清空Model相关字段数据 */
  clearRedux: generateEffect('fileManagement/clearRedux', { loading: false }),
};

// @ts-expect-error
@connect(mapStateToProps, mapDispatchToProps)
class FileManagementHome extends PureComponent<FileManagementProps, FileManagementState> {
  unlisten:() => void = _.noop;

  constructor(props: FileManagementProps) {
    super(props);
    this.state = {
      // 当前页面处于何种面板,初始化【文档管理]
      currentPannel: [Pannels.FileListPannel],
      // 是否批量操作
      batchOperation: false,
      // 列表选择功能的配置
      rowSelection: undefined,
      // 批量操作相关按钮是否禁用
      batchOperationButDisabled: true,
      // 文件批量上传面板
      fileUploadPanelVisible: false,
      // 文件解析面板
      fileAnalysisPannelVisible: false,
      // 文件解析切片面板
      fileAnalysisChunksPannelVisible: false,
      // 文档召回测试面板
      fileRecallTestPannelVisible: false,
      // 类目和文档的关联关系id列表
      relationIdList: [],
      // 文件解析面板、文件解析的切片面板需要用到的fileId列表
      fileIdList: [],
      // 类目弹框标题
      categoryModalTitle: '',
      // 类目弹框是否可见
      categoryModalVisible: false,
      // 类目弹框类型
      categoryModalType: '',
      // 类目弹框所选的文件类目
      categoryValue: [],
      // 筛选条件数据
      filterInfoData: {
        // 文件名称
        fileName: '',
        // 类目
        categoryId: [],
        // 数据来源
        dataSourceId: [],
        // 切片是否校验
        chunkVerifyStatus: [],
        // 文件状态
        fileStatus: [],
      },
      // 分页数据
      paginationData: {
        pageNum: 1,
        pageSize: 20,
      },
      // 当前location中的query
      currentLocationQuery: history.location.query,
    };
  }

  // 设置面包屑
  setBreadcrumb = (breadcrumbList: MenuDataItem[]) => {
    this.props.setBreadcrumb(breadcrumbList);
  };

  initPannel = () => {
    this.setState({
      currentPannel: [Pannels.FileListPannel],
    });

    // 清空query
    const { pathname } = location;
    replace({
      pathname,
      query: {},
    });
  }

  openPannel = (pannelName: PannelsName) => {
    const { currentPannel } = this.state;
    const currentPannelFinal = [...currentPannel, pannelName];

    this.setState({
      currentPannel: currentPannelFinal,
    });

    const { pathname } = location;
    // @ts-expect-error
    replace({
      pathname,
      query: {
        step: currentPannelFinal?.length,
      },
    });
  }

  closePannel = (pannelName: PannelsName) => {
    const { currentPannel } = this.state;
    const currentPannelFinal = _.filter(currentPannel, item => item !== pannelName);

    this.setState({
      currentPannel: currentPannelFinal,
    });

    const { pathname } = location;
    // @ts-expect-error
    replace({
      pathname,
      query: {
        step: currentPannelFinal?.length,
      },
    });
  }

  clearFileInfoList = () => {
    this.props.clearRedux({
      fileInfoList: [],
      fileAnalysisResult: [],
    });
  }

  // 查询文件管理列表
  queryFileManagementListInfo = (
    newFilterInfoData?: Partial<FilterInfoDataType>,
    newPaginationData?: Partial<PaginationDataType>,
  ) => {
    const { queryFileManagementList, tenantId } = this.props;
    const { filterInfoData, paginationData } = this.state;
    // 合并筛选条件数据
    const searchFilterInfoData = {
      ...filterInfoData,
      ...paginationData,
      ...newFilterInfoData,
      ...newPaginationData,
    };
    queryFileManagementList({
      tenantId,
      ...searchFilterInfoData,
    });
  };

  // 单个或批量编辑类目
  updateFileCategoryInfo = (isBatchUpdate: boolean, updateFileParams: any) => {
    const { tenantId } = this.props;
    const { relationIdList, batchOperation } = this.state;
    let updateFileCategoryParams = {} as any;
    if (isBatchUpdate) {
      // 批量更新【类目】
      updateFileCategoryParams = {
        tenantId,
        id: relationIdList,
        // 取类目的叶子节点
        fileCategoryId: _.last(updateFileParams),
      };
    } else {
      // 列表的单个更新【类目】
      const { value, relationId } = updateFileParams;
      updateFileCategoryParams = {
        tenantId,
        id: [relationId],
        // 取类目的叶子节点
        fileCategoryId: _.last(value),
      };
    }
    // 调用编辑类目，并重新查询列表接口刷新列表数据
    this.props.updateFileCategory(updateFileCategoryParams).then((result: boolean) => {
      if (result) {
        // 如果是批量操作，则取消
        if (batchOperation) {
          this.cancelOperation();
        }
        // 刷新列表
        this.queryFileManagementListInfo();
      }
    });
  };

  // 编辑是否启用
  updateFileStatusInfo = (updateFileParams: any) => {
    const { tenantId } = this.props;
    const { value, fileId } = updateFileParams;
    const updateFileStatusParams = {
      fileId,
      isEnabled: value,
      tenantId,
    };
    // 调用编辑是否启用，并重新查询列表接口刷新列表数据
    this.props.updateFileStatus(updateFileStatusParams).then((result: boolean) => {
      if (result) {
        // 刷新列表
        this.queryFileManagementListInfo();
      }
    });
  };

  componentDidUpdate(prevProps: Readonly<FileManagementProps>, prevState: FileManagementState) {
    const { queryFileCategory, tenantId } = this.props;
    const { currentLocationQuery } = prevState;
    const { currentLocationQuery: nextLocationQuery } = this.state;
    // 如果租户id tenantId 改变（切换租户），则重新查询文件列表和文件类目
    if (!_.isEqual(prevProps.tenantId, tenantId)) {
      this.queryFileManagementListInfo();
      // 查询文件类目
      queryFileCategory({ tenantId });
      // 取消批量操作
      this.cancelOperation();
    }
    // 如果step被清空，则回到列表页面
    if(currentLocationQuery?.step !== nextLocationQuery?.step && !nextLocationQuery?.step) {
      this.handleFileRecallTestPannelComplete();
      // 查询文件管理列表
      this.queryFileManagementListInfo();
    }
  }

  componentDidMount() {
    const { queryDataSources, queryFileCategory, tenantId, queryFileManageDict } = this.props;
    // 查询数据来源
    queryDataSources();
    // 查询文件类目
    queryFileCategory({ tenantId });
    // 查询文件管理字典
    queryFileManageDict();
    // 查询文件管理列表
    this.queryFileManagementListInfo();
    // 添加路由变化监听器
    this.unlisten = history.listen((router) => {
      this.setState({
        currentLocationQuery: router.query,
      });
    });
  }

  componentWillUnmount() {
    // 组件卸载时移除监听器
    this.unlisten();
  }

  // 筛选条件信息change
  filterInfoChange = (filterFieldName: string, value: any) => {
    const { filterInfoData, paginationData } = this.state;
    let newValue = value;
    // 将vlue二维数组展开平铺成一维数组
    if (
      filterFieldName === 'dataSourceId' ||
      filterFieldName === 'chunkVerifyStatus' ||
      filterFieldName === 'fileStatus'
    ) {
      newValue = _.flatten(value);
    } else if (filterFieldName === 'categoryId') {
      newValue = _.flatMap(value, item => _.last(item));
    }
    // 处理筛选条件数据
    const newFilterInfoData = {
      ...filterInfoData,
      [filterFieldName]: newValue,
    };
    // 更新筛选条件并重置到第一页
    const newPaginationData = {
      pageNum: 1,
      pageSize: paginationData.pageSize,
    };
    this.setState({
      filterInfoData: newFilterInfoData,
      paginationData: newPaginationData,
    });
    // 查询文件管理列表
    this.queryFileManagementListInfo(newFilterInfoData, newPaginationData);
  };

  // 列表选择框change事件
  tableSelectChange = (selectedRowKeys: string[], selectedRows: any) => {
    const batchOperationButDisabled = _.isEmpty(selectedRowKeys);
    const relationIdList = _.map(selectedRows, item => item.relationId);
    this.setState({
      relationIdList,
      fileIdList: selectedRowKeys,
      batchOperationButDisabled,
    });
  };

  // 批量管理按钮事件
  updateBatchOperation = () => {
    this.setState({
      batchOperation: true,
      rowSelection: {
        onChange: this.tableSelectChange,
      },
    });
  };

  // 取消批量操作
  cancelOperation = () => {
    this.setState({
      batchOperation: false,
      rowSelection: undefined,
      fileIdList: [],
      batchOperationButDisabled: true,
    });
  };

  // NOTE: SWB 2025-04-14 选择条目后点击【批量编辑】则先打开【文件解析】面板
  handleBatchEdit = () => {
    const { fileIdList } = this.state;

    if (!_.isEmpty(fileIdList)) {
      this.setState({
        // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
        // fileAnalysisPannelVisible: true,
        fileAnalysisChunksPannelVisible: true,
      });

      // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
      // this.openPannel(Pannels.FileAnalysisPannel);
      this.openPannel(Pannels.FileChunkPannel);
      // 设置面包屑
      this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }, { name: '文件导入' }]);
    }
  };

  // 命中测试(单个及批量)
  onHitTest = (fileId?: string) => {
    const { fileIdList } = this.state;
    // 列表的单个命中测试
    if (fileId) {
      this.setState({
        fileIdList: [fileId],
        fileRecallTestPannelVisible: true,
      });
    } else if (!_.isEmpty(fileIdList)) {
      // 批量命中测试，打开命中测试面板
      this.setState({
        fileRecallTestPannelVisible: true,
      });
    }

    this.openPannel(Pannels.FileRecallTestPannel);
    // 设置面包屑
    this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }, { name: '文件导入' }]);
  };

  // 打开类目弹框
  openCategoryModal = (type: string) => {
    this.setState({
      categoryModalTitle: type === 'batchEditCategory' ? '批量编辑类目' : '确认导入类目',
      categoryModalType: type,
      categoryModalVisible: true,
    });
  };

  // 列表的编辑操作
  onEdit = (fileId: string) => {
    this.setState({
      fileIdList: [fileId],
      // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
      // fileAnalysisPannelVisible: true,
      fileAnalysisChunksPannelVisible: true,
    });

    // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
    // this.openPannel(Pannels.FileAnalysisPannel);
    this.openPannel(Pannels.FileChunkPannel);
    // 设置面包屑
    this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }, { name: '文件导入' }]);
  };

  // 列表的删除操作
  onDelete = (fileId: string) => {
    const { deleteFile, tenantId } = this.props;
    // 调用删除接口，并重新调用列表接口刷新列表数据
    deleteFile({ fileId, tenantId }).then((result: boolean) => {
      if (result) {
        // 刷新列表
        this.queryFileManagementListInfo();
      }
    });
  };

  // 列表分页change事件
  onPageChange = (pageNum: number, pageSize: number) => {
    const newPaginationData = {
      pageNum,
      pageSize,
    };
    this.setState({ paginationData: newPaginationData });
    this.queryFileManagementListInfo({}, newPaginationData);
  };

  // 列表的更新【类目】或【是否启用】
  onFileListUpdate = (fieldName: string, value: string | (string | number)[], id: string) => {
    if (fieldName === 'fileCategory') {
      // 单个文件更新【类目】
      this.updateFileCategoryInfo(false, { value, relationId: id });
    } else {
      // 单个文件更新【是否启用】
      this.updateFileStatusInfo({ value, fileId: id });
    }
  }

  handleFileAnalysisPannelPrevStep = () => {
    const  { fileUploadPanelVisible } = this.state;

    // NOTE: SWB 2025-04-28 如果【文件解析】页面是由【列表面板】点击【编辑】或者【批量编辑】进入，则需要修改下顶部面包屑
    // 【文件上传】面板不可见时就代表是从【列表面板】进入
    if (!fileUploadPanelVisible) {
      this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }]);
      // 清空step
      const { pathname } = location;
      replace({
        pathname,
        query: {},
      });
    }

    this.setState({
      fileAnalysisPannelVisible: false,
    }, this.clearFileInfoList);

    this.closePannel(Pannels.FileAnalysisPannel);
  };

  handleFileAnalysisPannelonNextStep = () => {
    this.setState({
      fileAnalysisChunksPannelVisible: true,
    });

    this.openPannel(Pannels.FileChunkPannel);
  };

  handleFileAnalysisChunksPannelPrevStep = () => {
    const  { fileUploadPanelVisible } = this.state;

    // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
    // 因此在【文件切片面板】中按【上一步】时，需要修改面包屑
    if (!fileUploadPanelVisible) {
      this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }]);
      // 清空step
      const { pathname } = location;
      replace({
        pathname,
        query: {},
      });

      this.clearFileInfoList();
    }

    this.setState({
      fileAnalysisChunksPannelVisible: false,
    });

    this.closePannel(Pannels.FileChunkPannel);
  };

  handleFileAnalysisChunksPannelonNextStep = () => {
    this.setState({
      fileRecallTestPannelVisible: true,
    });

    this.openPannel(Pannels.FileRecallTestPannel);
  };

  handleFileRecallTestPannelPrevStep = () => {
    const  {
      fileUploadPanelVisible,
      // fileAnalysisPannelVisible,
      fileAnalysisChunksPannelVisible,
    } = this.state;

    // NOTE: SWB 2025-04-28 如果【命中测试】页面是由【列表面板】点击【命中测试】或者【批量命中测试】进入，则需要修改下顶部面包屑
    // 【文件上传】面板和【文件解析】面板不可见时就代表是从【列表面板】进入
    // NOTE: SWB 2025-09-05 【文件解析】面板暂时隐藏，改为直接【文件切片面板】
    if (!fileUploadPanelVisible && !fileAnalysisChunksPannelVisible) {
      this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }]);
      this.clearFileInfoList();
    }

    this.closePannel(Pannels.FileRecallTestPannel);

    this.setState({
      fileRecallTestPannelVisible: false,
    });
  };

  handleFileRecallTestPannelComplete = () => {
    // 【完成】返回到【列表面板】
    this.setState({
      fileUploadPanelVisible: false,
      fileAnalysisPannelVisible: false,
      fileAnalysisChunksPannelVisible: false,
      fileRecallTestPannelVisible: false,
    }, this.clearFileInfoList);

    this.initPannel();

    this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }]);
  }

  // 类目弹框ok
  handleCategoryModalOk = (categoryValue: (string | number)[]) => {
    const { categoryModalType } = this.state;
    // 保存选择的类目
    this.setState({ categoryValue });
    if (categoryModalType === 'batchEditCategory') {
      // 批量更新类目
      this.updateFileCategoryInfo(true, categoryValue);
    } else {
      // 导入类目，打开文件导入面板
      this.setState({
        fileUploadPanelVisible: true,
      });
      this.openPannel(Pannels.FileUploadPannel);
      // 设置面包屑
      this.setBreadcrumb([{ name: '知识库管理' }, { name: '文件管理' }, { name: '文件导入' }]);
    }
    this.handleCategoryModalCancel();
  };

  // 类目弹框cancel
  handleCategoryModalCancel = () => {
    this.setState({
      categoryModalTitle: '',
      categoryModalType: '',
      categoryModalVisible: false,
    });
  };


  handleFileUploadPanelVisibleNextStep = (fileIdList: string[]) => {
    this.setState({
      // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
      // fileAnalysisPannelVisible: true,
      fileAnalysisChunksPannelVisible: true,
      fileIdList,
    });

    // NOTE: SWB 2025-09-02 知识库2期，需要先把【文件解析面板】隐藏，直接打开【文件切片面板】
    // this.openPannel(Pannels.FileAnalysisPannel);
    this.openPannel(Pannels.FileChunkPannel);
  }

  // 顶部标题右侧按钮信息
  rightInfo = () => {
    const { userInfo: { roleInfo } } = this.props;
    const { batchOperation, batchOperationButDisabled } = this.state;
    return (
      <TopRightButton
        batchOperation={batchOperation}
        batchOperationButDisabled={batchOperationButDisabled}
        roleInfo={roleInfo}
        updateBatchOperation={this.updateBatchOperation}
        cancelOperation={this.cancelOperation}
        onBatchEdit={this.handleBatchEdit}
        hitTest={this.onHitTest}
        openCategoryModal={this.openCategoryModal}
      />
    );
  };

  render() {
    const {
      currentPannel,
      rowSelection,
      fileAnalysisPannelVisible,
      fileUploadPanelVisible,
      fileIdList,
      fileAnalysisChunksPannelVisible,
      categoryModalTitle,
      categoryModalVisible,
      categoryValue,
      fileRecallTestPannelVisible,
    } = this.state;
    const { fileCategory, dataSources, fileManageDict, fileManagementList, userInfo,
      queryFileManagementListLoading } = this.props;
    const { roleInfo } = userInfo;

    // 取类目的根节点最后一个值作为 categoryId
    const categoryId = _.toString(_.last(categoryValue));

    return (
      <div className={styles.fileManagement}>
        {/* 顶部标题 */}
        <TopTitleInfo title="文件管理" rightInfo={this.rightInfo} />
        {/* 筛选条件信息 */}
        <FilterInfo
          fileCategory={fileCategory}
          dataSources={dataSources}
          fileManageDict={fileManageDict}
          filterInfoChange={this.filterInfoChange}
        />
        {/* 列表 */}
        <FileTable
          rowSelection={rowSelection}
          fileCategory={fileCategory}
          roleInfo={roleInfo}
          fileManagementList={fileManagementList}
          loading={queryFileManagementListLoading}
          handleEdit={this.onEdit}
          handleHitTest={this.onHitTest}
          handleDelete={this.onDelete}
          handlePageChange={this.onPageChange}
          handleFileListUpdate={this.onFileListUpdate}
        />
        {/* 文件上传面板 */}
        {
          fileUploadPanelVisible
            ? (
              <FileUploadPanel
                categoryId={categoryId}
                onNextStep={this.handleFileUploadPanelVisibleNextStep}
              />
            )
            : null
        }
        {/* 文件解析面板 */}
        {
          fileAnalysisPannelVisible
            ? (
              <FileAnalysisPannel
                fileIdList={fileIdList}
                currentPannel={currentPannel}
                onPrevStep={this.handleFileAnalysisPannelPrevStep}
                onNextStep={this.handleFileAnalysisPannelonNextStep}
              />
            )
            : null
        }
        {/* 文件切片面板 */}
        {
          fileAnalysisChunksPannelVisible
            ? (
              <FileAnalysisChunksPannel
                fileIdList={fileIdList}
                currentPannel={currentPannel}
                onPrevStep={this.handleFileAnalysisChunksPannelPrevStep}
                onNextStep={this.handleFileAnalysisChunksPannelonNextStep}
              />
            )
            : null
        }
        {/* 文档召回测试面板 */}
        {
          fileRecallTestPannelVisible
            ? (
              <FileRecallTestPannel
                fileIdList={fileIdList}
                currentPannel={currentPannel}
                onPrevStep={this.handleFileRecallTestPannelPrevStep}
                onComplete={this.handleFileRecallTestPannelComplete}
              />
            )
            : null
        }
        {/* 类目弹框 */}
        {
          categoryModalVisible ? (
            <CategoryModal
              title={categoryModalTitle}
              fileCategory={fileCategory}
              onOk={this.handleCategoryModalOk}
              onCancel={this.handleCategoryModalCancel}
            />
          ) : null
        }
      </div>
    );
  }
}

// 初始化面包屑
const breadcrumbs = [{ name: '知识库管理' }, { name: '文件管理' }];
// @ts-expect-error
export default WithCustomBreadcrumb(breadcrumbs)(FileManagementHome);
