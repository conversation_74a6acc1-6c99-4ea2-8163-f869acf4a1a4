import type { Dispatch, History } from '@oula/oula';
import type { MenuDataItem } from '@umijs/route-utils';
import type {
  ConversationInfoResult,
  IQueryConversationInfo,
} from '@/services/types/api/dataFeedback/queryConversationInfo';
import type {
  ConversationDetailsResult,
  IQueryConversationDetails,
  ISubmitOperationAnalysis,
} from '@/services/types/api/dataFeedback/queryConversationDetails';

export interface ConversationDetailProps {
  dispatch: Dispatch;
  history: History;
  /** 会话信息 */
  conversationInfo: ConversationInfoResult;
  /** 会话详情 */
  conversationDetails: ConversationDetailsResult;
  /** 查询会话详情loading */
  queryConversationDetailsLoading: boolean;
  /** 查询会话信息 */
  queryConversationInfo: (query: IQueryConversationInfo['Query']) => Promise<unknown>;
  /** 查询会话详情 */
  queryConversationDetails: (query: IQueryConversationDetails['Query']) => Promise<unknown>;
  /** 提交运营分析 */
  submitOperationAnalysis: (query: ISubmitOperationAnalysis['Query']) => Promise<unknown>;
  /** 设置面包屑 */
  setBreadcrumb: (breadcrumbList: MenuDataItem[]) => void;
}
