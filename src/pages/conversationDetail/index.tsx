/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-22
 * @Description：会话详情
*/
import type { ConnectState } from '@/models/connect';
import type { MenuDataItem } from '@umijs/route-utils';
import type { CheckboxValueType } from '@ht/sprite-ui/es/checkbox/Group';
import type { ConversationDetailProps } from './data';

import React, { PureComponent } from 'react';
import _ from 'lodash';
import { connect } from '@oula/oula';

import { generateEffect } from '@/utils/dvaHelper';
import WithCustomBreadcrumb from '@/components/common/WithCustomBreadcrumb';
import TopTitleInfo from '@/components/KnowledgeWarehouseManagement/FileManagement/TopTitleInfo';
import ConversationInfo from '@/components/ConversationDetail/ConversationInfo';
import ConversationDetailInfo from '@/components/ConversationDetail/ConversationDetailInfo';

import styles from './index.less';

const mapStateToProps = ({ dataFeedback, loading }: ConnectState) => ({
  /** 会话信息 */
  conversationInfo: dataFeedback.conversationInfo,
  /** 会话详情 */
  conversationDetails: dataFeedback.conversationDetails,
  // 查询会话详情loading
  queryConversationDetailsLoading: loading.effects['dataFeedback/queryConversationDetails'],
});

const mapDispatchToProps = {
  /** 查询会话信息 */
  queryConversationInfo: generateEffect('dataFeedback/queryConversationInfo'),
  /** 查询会话详情 */
  queryConversationDetails: generateEffect('dataFeedback/queryConversationDetails'),
  /** 提交运营分析 */
  submitOperationAnalysis: generateEffect('dataFeedback/submitOperationAnalysis'),
};

// @ts-expect-error
@connect(mapStateToProps, mapDispatchToProps)
class ConversationDetail extends PureComponent<ConversationDetailProps> {
  constructor(props: ConversationDetailProps) {
    super(props);
    this.state = {};
  }

  // 设置面包屑
  setBreadcrumb = (breadcrumbList: MenuDataItem[]) => {
    this.props.setBreadcrumb(breadcrumbList);
  };

  // 查询会话详情
  queryConversationDetailsInfo = (params: any) => {
    const { queryConversationDetails, history: { location: { query } } } = this.props;
    const { conversationId, empId } = query as any;
    queryConversationDetails({
      appId: 'aorta',
      pageNum: 1,
      pageSize: 100,
      conversationId,
      empId,
      ...params,
    });
  };

  componentDidMount() {
    const { queryConversationInfo, history: { location: { query } } } = this.props;
    const { conversationId, breadcrumbName } = query as any;
    // 查询会话信息
    queryConversationInfo({ conversationId });
    // 查询会话详情
    this.queryConversationDetailsInfo({ feedbackResult: '' });
    // 设置面包屑
    this.setBreadcrumb([{ name: '问TA管理平台' }, { name: breadcrumbName }]);
  }

  // 会话详情的回答类型改变
  conversationDetailTypeChange = (checkedValues: CheckboxValueType[]) => {
    let feedbackResult = '';
    if (_.size(checkedValues) === 1) {
      feedbackResult = checkedValues?.[0] as string;
    }
    // 查询会话详情
    this.queryConversationDetailsInfo({ feedbackResult });
  };

  render() {
    const { conversationInfo, conversationDetails, queryConversationDetailsLoading,
      submitOperationAnalysis,
    } = this.props;

    return (
      <div className={styles.conversationDetail}>
        {/* 顶部标题 */}
        <TopTitleInfo title="会话详情" />
        <div className={styles.conversationDetailContent}>
          <ConversationInfo conversationInfo={conversationInfo} />
          <ConversationDetailInfo
            conversationDetails={conversationDetails}
            queryConversationDetailsLoading={queryConversationDetailsLoading}
            conversationDetailTypeChange={this.conversationDetailTypeChange}
            submitOperationAnalysis={submitOperationAnalysis}
          />
        </div>
      </div>
    );
  }
}

// @ts-expect-error
export default WithCustomBreadcrumb([])(ConversationDetail);
