import type { GlobalModelState } from './global';
import type { FileManagementModelState } from './fileManagement';
import type { DataFeedbackModelState } from './dataFeedback';
import type { ConversationRecordModelState } from './conversationRecord';
import type { StateType } from './login';

export { GlobalModelState, FileManagementModelState };

export type Loading = {
  global: boolean;
  effects: Record<string, boolean | undefined>;
  models: {
    global?: boolean;
    login?: boolean;
    fileManagement?: boolean;
  };
};

export type ConnectState = {
  loading: Loading;
  global: GlobalModelState;
  login: StateType;
  fileManagement: FileManagementModelState;
  dataFeedback: DataFeedbackModelState;
  conversationRecord: ConversationRecordModelState;
};

export type Route = {
  routes?: Route[];
};
