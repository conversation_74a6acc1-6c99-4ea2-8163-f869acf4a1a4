/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-15
 * @Description：数据反馈Model
 */
import type { Effect, Reducer } from '@oula/oula';
import type {
  FeedbackPersonResult,
  IQueryFeedbackPerson,
} from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type {
  DepartmentInfoItem,
  IQueryDepartmentInfo,
} from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type {
  DictResult,
  IQueryFeedbackTagsAndAgentDict,
} from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type {
  DataFeedbackList,
  IQueryDataFeedbackList,
} from '@/services/types/api/dataFeedback/queryDataFeedbackList';
import type {
  ConversationInfoResult,
  IQueryConversationInfo,
} from '@/services/types/api/dataFeedback/queryConversationInfo';
import type {
  ConversationDetailsResult,
  IQueryConversationDetails,
  ISubmitOperationAnalysis,
} from '@/services/types/api/dataFeedback/queryConversationDetails';

import {
  queryFeedbackPerson,
  queryDepartmentInfo,
  queryFeedbackTagsAndAgentDict,
  queryDataFeedbackList,
  queryConversationInfo,
  queryConversationDetails,
  submitOperationAnalysis,
} from '@/services/dataFeedback';

export interface DataFeedbackModelState {
  /** 反馈人 */
  feedbackPerson: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  /** 数据反馈列表 */
  dataFeedbackList: DataFeedbackList;
  /** 会话信息 */
  conversationInfo: ConversationInfoResult;
  /** 会话详情 */
  conversationDetails: ConversationDetailsResult;
}

export interface DataFeedbackModelType {
  namespace: string;
  state: DataFeedbackModelState;
  reducers: {
    queryFeedbackPersonSuccess: Reducer<DataFeedbackModelState>;
    queryDepartmentInfoSuccess: Reducer<DataFeedbackModelState>;
    queryFeedbackTagsAndAgentDictSuccess: Reducer<DataFeedbackModelState>;
    queryDataFeedbackListSuccess: Reducer<DataFeedbackModelState>;
    queryConversationInfoSuccess: Reducer<DataFeedbackModelState>;
    queryConversationDetailsSuccess: Reducer<DataFeedbackModelState>;
  };
  effects: {
    queryFeedbackPerson: Effect;
    queryDepartmentInfo: Effect;
    queryFeedbackTagsAndAgentDict: Effect;
    queryDataFeedbackList: Effect;
    queryConversationInfo: Effect;
    queryConversationDetails: Effect;
    submitOperationAnalysis: Effect;
  };
}

const DataFeedbackModel: DataFeedbackModelType = {
  namespace: 'dataFeedback',
  state: {
    /** 反馈人 */
    feedbackPerson: {} as FeedbackPersonResult,
    /** 所属部门 */
    department: [],
    /** 反馈标签和Agent字典 */
    dictData: {} as DictResult,
    /** 数据反馈列表 */
    dataFeedbackList: {} as DataFeedbackList,
    /** 会话信息 */
    conversationInfo: {} as ConversationInfoResult,
    /** 会话详情 */
    conversationDetails: {} as ConversationDetailsResult,
  },
  effects: {
    // 查询反馈人
    *queryFeedbackPerson({ payload }, { call, put }) {
      const response: IQueryFeedbackPerson['Response'] = yield call(queryFeedbackPerson, payload);
      yield put({
        type: 'queryFeedbackPersonSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询所属部门
    *queryDepartmentInfo({ payload }, { call, put }) {
      const response: IQueryDepartmentInfo['Response'] = yield call(queryDepartmentInfo, payload);
      yield put({
        type: 'queryDepartmentInfoSuccess',
        payload: response?.resultData || [],
      });
    },
    // 查询反馈标签和Agent字典
    *queryFeedbackTagsAndAgentDict({ payload }, { call, put }) {
      const response: IQueryFeedbackTagsAndAgentDict['Response'] = yield call(queryFeedbackTagsAndAgentDict, payload);
      yield put({
        type: 'queryFeedbackTagsAndAgentDictSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询数据反馈列表
    *queryDataFeedbackList({ payload }, { call, put }) {
      const response: IQueryDataFeedbackList['Response'] = yield call(queryDataFeedbackList, payload);
      yield put({
        type: 'queryDataFeedbackListSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询会话信息
    *queryConversationInfo({ payload }, { call, put }) {
      const response: IQueryConversationInfo['Response'] = yield call(queryConversationInfo, payload);
      yield put({
        type: 'queryConversationInfoSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询会话详情
    *queryConversationDetails({ payload }, { call, put }) {
      const response: IQueryConversationDetails['Response'] = yield call(queryConversationDetails, payload);
      yield put({
        type: 'queryConversationDetailsSuccess',
        payload: response?.resultData || {},
      });
    },
    // 提交运营分析
    *submitOperationAnalysis({ payload }, { call, put }) {
      const response: ISubmitOperationAnalysis['Response'] = yield call(submitOperationAnalysis, payload);
      // yield put({
      //   type: 'submitOperationAnalysisSuccess',
      //   payload: response?.resultData || {},
      // });
      return response;
    },
  },
  reducers: {
    queryFeedbackPersonSuccess(state, action) {
      return {
        ...state,
        feedbackPerson: action?.payload,
      };
    },
    queryDepartmentInfoSuccess(state, action) {
      return {
        ...state,
        department: action?.payload,
      };
    },
    queryFeedbackTagsAndAgentDictSuccess(state, action) {
      return {
        ...state,
        dictData: action?.payload,
      };
    },
    queryDataFeedbackListSuccess(state, action) {
      return {
        ...state,
        dataFeedbackList: action?.payload,
      };
    },
    queryConversationInfoSuccess(state, action) {
      return {
        ...state,
        conversationInfo: action?.payload,
      };
    },
    queryConversationDetailsSuccess(state, action) {
      return {
        ...state,
        conversationDetails: action?.payload,
      };
    },
  },
};

export default DataFeedbackModel;
