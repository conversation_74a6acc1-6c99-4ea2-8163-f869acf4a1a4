import type { Reducer, ImmerReducer, Effect } from '@oula/oula';
import type { IQueryUserInfo, IUserInfo } from '@/services/types/api/login/queryEmpInfo';
import type { IGetTenantsInfo, ITenantListItm } from '@/services/types/api/login/getTenantsInfo';
import type { IMenuListItem, IQueryMenuInfo } from '@/services/types/api/login/queryMenu';
import type { MenuDataItem } from '@umijs/route-utils';
import { getTenantsInfo, queryMenu, queryUserInfo } from '@/services/login';

export type GlobalModelState = {
  /** 自定义面包屑 */
  customBreadcrumb: MenuDataItem[];
  /** 左侧菜单是否折叠 */
  collapsed: boolean;
  /** 租户id */
  tenantId: string;
  /** 用户信息 */
  userInfo: IUserInfo;
  /** 租户列表 */
  tenantsList: ITenantListItm[];
  /** 菜单列表 */
  menuList: IMenuListItem[];
};

export type GlobalModelType = {
  namespace: 'global';
  state: GlobalModelState;
  effects: {
    queryUserInfo: Effect;
    getTenantsInfo: Effect;
    queryMenu: Effect;
  };
  reducers: {
    changeLayoutCollapsed: Reducer<GlobalModelState>;
    changeTenantId: Reducer<GlobalModelState>;
    demoReducer: ImmerReducer<GlobalModelState>;
    queryUserInfoSuccess: ImmerReducer<GlobalModelState>;
    getTenantsInfoSuccess: ImmerReducer<GlobalModelState>;
    setCustomBreadcrumb: ImmerReducer<GlobalModelState>;
    queryMenuSuccess: ImmerReducer<GlobalModelState>;
  };
};

const GlobalModel: GlobalModelType = {
  namespace: 'global',

  state: {
    // 自定义面包屑
    customBreadcrumb: [],
    // 左侧菜单是否折叠
    collapsed: false,
    // 租户id
    tenantId: '',
    // 用户信息
    userInfo: {} as IUserInfo,
    // 租户列表
    tenantsList: [] as ITenantListItm[],
    // 菜单列表
    menuList: [],
  },

  effects: {
    // 获取用户信息
    *queryUserInfo({ payload }, { call, put }) {
      const response: IQueryUserInfo['Response'] = yield call(queryUserInfo, payload);
      yield put({
        type: 'queryUserInfoSuccess',
        payload: response?.resultData || {},
      });
      return response;
    },

    // 获取租户列表（所有的租户列表）
    *getTenantsInfo({ payload }, { call, put }) {
      const response: IGetTenantsInfo['Response'] = yield call(getTenantsInfo, payload);
      yield put({
        type: 'getTenantsInfoSuccess',
        payload: response?.resultData || [],
      });
    },

    // 获取菜单
    *queryMenu({ payload }, { call, put }) {
      const response: IQueryMenuInfo['Response'] = yield call(queryMenu, payload);
      yield put({
        type: 'queryMenuSuccess',
        payload: response?.resultData || {},
      });
    },
  },

  reducers: {
    changeLayoutCollapsed(state, action): GlobalModelState {
      return {
        ...state,
        collapsed: action.payload,
      };
    },
    changeTenantId(state, action): GlobalModelState {
      return {
        ...state,
        tenantId: action.payload,
      };
    },
    demoReducer(state): GlobalModelState {
      return {
        ...state,
      };
    },
    queryUserInfoSuccess(state, action) {
      return {
        ...state,
        userInfo: action?.payload,
      };
    },
    getTenantsInfoSuccess(state, action) {
      return {
        ...state,
        tenantsList: action?.payload,
      };
    },
    setCustomBreadcrumb(state, action) {
      return {
        ...state,
        customBreadcrumb: action.payload,
      };
    },
    queryMenuSuccess(state, action) {
      return {
        ...state,
        menuList: action?.payload?.menuList || [],
      };
    },
  },
};

export default GlobalModel;
