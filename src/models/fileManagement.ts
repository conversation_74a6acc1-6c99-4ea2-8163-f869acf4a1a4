/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-04-10
 * @Description：知识库管理-文件管理Model
 */
import type { Effect, Reducer } from '@oula/oula';
import type { ApiResponse } from '@/services/types/api/common';
import type {
  IGetFileInfoList,
  IFileInfo,
} from '@/services/types/api/fileManagement/getFileInfoList';
import type {
  IGetFileAnalysisResult,
  IFileAnalysisResult,
} from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type {
  IGetAnalysisiMarkdownResult,
} from '@/services/types/api/fileManagement/getAnalysisMarkDown';
import type {
  IGetMarkDownChunks,
} from '@/services/types/api/fileManagement/getMarkDownChunks';
import type {
  DataSourcesItem,
  IQueryDataSources,
} from '@/services/types/api/fileManagement/queryDataSources';
import type {
  FileCategoryItem,
  IQueryFileCategory,
} from '@/services/types/api/fileManagement/queryFileCategory';
import type {
  FileManagementList,
  IQueryFileManagementList,
} from '@/services/types/api/fileManagement/queryFileManagementList';
import type {
  IUploadFileRequest,
} from '@/services/types/api/fileManagement/uploadFile';
import type {
  IGetPreviewUrl,
} from '@/services/types/api/fileManagement/getPreviewUrl';
import type {
  IRecallTest,
} from '@/services/types/api/fileManagement/recallTest';
import type {
  ISaveAnalysisMarkDown,
} from '@/services/types/api/fileManagement/saveAnalysisMarkDown';
import type {
  IDeleteChunk,
} from '@/services/types/api/fileManagement/deleteChunk';
import type {
  ISaveChunk,
} from '@/services/types/api/fileManagement/saveChunk';
import type {
  ICheckoutChunk,
} from '@/services/types/api/fileManagement/checkoutChunk';
import type {
  IGetFileChunkProcess,
  IFileChunkProcessResult,
} from '@/services/types/api/fileManagement/getFileChunkProcess';

import {
  getFileInfoList,
  getFileAnalysisResult,
  getAnalysisMarkDown,
  getPreviewUrl,
  getMarkDownChunks,
  queryDataSources,
  queryFileCategory,
  queryDict,
  queryFileManagementList,
  updateFileCategory,
  updateFileStatus,
  deleteFile,
  uploadFile,
  recallTest,
  saveAnalysisMarkDown,
  deleteChunk,
  saveChunk,
  checkoutChunk,
  getFileChunkProcess,
} from '@/services/fileManagement';

export interface FileManagementModelState {
  /** 文档信息列表 */
  fileInfoList: IFileInfo[];
  /** 文件解析进度结果 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 文件分片进度结果 */
  fileChunkProcessResult: IFileChunkProcessResult[];
  /** 数据来源 */
  dataSources: DataSourcesItem[];
  /** 文件类目 */
  fileCategory: FileCategoryItem[];
  fileManageDict: Record<string, any>;
  /** 文件管理列表 */
  fileManagementList: FileManagementList;
}

export interface FileManagementModelType {
  namespace: string;
  state: FileManagementModelState;
  reducers: {
    getFileInfoListSuccess: Reducer<FileManagementModelState>;
    getFileAnalysisResultSuccess: Reducer<FileManagementModelState>;
    getFileChunkProcessSuccess: Reducer<FileManagementModelState>;
    queryDataSourcesSuccess: Reducer<FileManagementModelState>;
    queryFileCategorySuccess: Reducer<FileManagementModelState>;
    queryFileManageDictSuccess: Reducer<FileManagementModelState>;
    queryFileManagementListSuccess: Reducer<FileManagementModelState>;
    clearRedux: Reducer<FileManagementModelState>;
  };
  effects: {
    getPreviewUrl: Effect;
    getFileInfoList: Effect;
    getFileAnalysisResult: Effect;
    getAnalysisMarkDown: Effect;
    saveAnalysisMarkDown: Effect;
    getMarkDownChunks: Effect;
    queryDataSources: Effect;
    queryFileCategory: Effect;
    queryFileManageDict: Effect;
    queryFileManagementList: Effect;
    recallTest: Effect;
    updateFileCategory: Effect;
    updateFileStatus: Effect;
    deleteFile: Effect;
    uploadFile: Effect;
    deleteChunk: Effect;
    saveChunk: Effect;
    checkoutChunk: Effect;
    getFileChunkProcess: Effect;
  };
}

const FileManagementModel: FileManagementModelType = {
  namespace: 'fileManagement',
  state: {
    fileInfoList: [],
    fileAnalysisResult: [],
    fileChunkProcessResult: [],
    dataSources: [] as DataSourcesItem[],
    fileCategory: [] as FileCategoryItem[],
    fileManageDict: {} as Record<string, any>,
    fileManagementList: {} as FileManagementList,
  },
  effects: {
    // 获取文档解析等页面需要的文档信息
    *getFileInfoList({ payload }, { call, put }) {
      const response: IGetFileInfoList['Response'] = yield call(getFileInfoList, payload);
      yield put({
        type: 'getFileInfoListSuccess',
        payload: response?.resultData || [],
      });
    },

    // 获取文件的解析结果
    *getFileAnalysisResult({ payload }, { call, put }) {
      const response: IGetFileAnalysisResult['Response'] = yield call(getFileAnalysisResult, payload);
      yield put({
        type: 'getFileAnalysisResultSuccess',
        payload: response?.resultData || [],
      });

      return response?.resultData || [];
    },

    // 获取文件的分片进程结果
    *getFileChunkProcess({ payload }, { call, put }) {
      const response: IGetFileChunkProcess['Response'] = yield call(getFileChunkProcess, payload);
      yield put({
        type: 'getFileChunkProcessSuccess',
        payload: response?.resultData || [],
      });

      return response?.resultData || [];
    },

    // 获取文件解析后的MarkDown文件内容
    *getAnalysisMarkDown({ payload }, { call }) {
      const response: IGetAnalysisiMarkdownResult['Response'] = yield call(getAnalysisMarkDown, payload);
      return response?.resultData || {};
    },

    // 保存MarkDown内容
    *saveAnalysisMarkDown({ payload }, { call }) {
      const response: ISaveAnalysisMarkDown['Response'] = yield call(saveAnalysisMarkDown, payload);
      return response?.resultData || false;
    },

    // 删除文档分片
    *deleteChunk({ payload }, { call }) {
      const response: IDeleteChunk['Response'] = yield call(deleteChunk, payload);
      return response?.resultData || false;
    },

    // 保存文档切片
    *saveChunk({ payload }, { call }) {
      const response: ISaveChunk['Response'] = yield call(saveChunk, payload);
      return response?.resultData || {};
    },

    // 一键校验切片
    *checkoutChunk({ payload }, { call }) {
      const response: ICheckoutChunk['Response'] = yield call(checkoutChunk, payload);
      return response?.resultData || false;
    },

    // 获取源文档预览地址
    *getPreviewUrl({ payload }, { call }) {
      const response: IGetPreviewUrl['Response'] = yield call(getPreviewUrl, payload);
      return response?.resultData || {};
    },

    // 获取文档切片列表
    *getMarkDownChunks({ payload }, { call }) {
      const response: IGetMarkDownChunks['Response'] = yield call(getMarkDownChunks, payload);
      return response?.resultData || [];
    },

    // 召回测试
    *recallTest({ payload }, { call }) {
      const response: IRecallTest['Response'] = yield call(recallTest, payload);
      return response?.resultData || [];
    },

    // 查询数据来源
    *queryDataSources({ payload }, { call, put }) {
      const response: IQueryDataSources['Response'] = yield call(queryDataSources, payload);
      yield put({
        type: 'queryDataSourcesSuccess',
        payload: response?.resultData || [],
      });
    },

    // 查询文件类目
    *queryFileCategory({ payload }, { call, put }) {
      const response: IQueryFileCategory['Response'] = yield call(queryFileCategory, payload);
      yield put({
        type: 'queryFileCategorySuccess',
        payload: response?.resultData || [],
      });
    },

    // 查询文件管理字典(切片校验状态、当前状态)
    *queryFileManageDict({ payload }, { call, put }) {
      const response: IQueryFileCategory['Response'] = yield call(queryDict, payload);
      yield put({
        type: 'queryFileManageDictSuccess',
        payload: response?.resultData || [],
      });
    },

    // 查询文件管理列表
    *queryFileManagementList({ payload }, { call, put }) {
      const response: IQueryFileManagementList['Response'] = yield call(queryFileManagementList, payload);
      yield put({
        type: 'queryFileManagementListSuccess',
        payload: response?.resultData || {},
      });
    },

    // 单个或批量编辑类目
    *updateFileCategory({ payload }, { call }) {
      const response: ApiResponse<boolean> = yield call(updateFileCategory, payload);
      return response?.resultData || false;
    },

    // 编辑是否启用
    *updateFileStatus({ payload }, { call }) {
      const response: ApiResponse<boolean> = yield call(updateFileStatus, payload);
      return response?.resultData || false;
    },

    // 删除文件
    *deleteFile({ payload }, { call }) {
      const response: ApiResponse<boolean> = yield call(deleteFile, payload);
      return response?.resultData || false;
    },

    // 上传文件
    *uploadFile({ payload }, { call }) {
      const response: IUploadFileRequest['Response'] = yield call(uploadFile, payload);
      return response?.resultData || {};
    },

  },
  reducers: {
    getFileInfoListSuccess(state, action) {
      return {
        ...state,
        fileInfoList: action?.payload,
      };
    },
    getFileAnalysisResultSuccess(state, action) {
      return {
        ...state,
        fileAnalysisResult: action?.payload,
      };
    },
    getFileChunkProcessSuccess(state, action) {
      return {
        ...state,
        fileChunkProcessResult: action?.payload,
      };
    },
    queryDataSourcesSuccess(state, action) {
      return {
        ...state,
        dataSources: action?.payload,
      };
    },
    queryFileCategorySuccess(state, action) {
      return {
        ...state,
        fileCategory: action?.payload,
      };
    },
    queryFileManageDictSuccess(state, action) {
      return {
        ...state,
        fileManageDict: action?.payload,
      };
    },
    queryFileManagementListSuccess(state, action) {
      return {
        ...state,
        fileManagementList: action?.payload,
      };
    },
    clearRedux(state, action) {
      return {
        ...state,
        ...action.payload || {},
      };
    },
  },
};

export default FileManagementModel;
