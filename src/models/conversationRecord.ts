/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录Model
 */
import type { Effect, Reducer } from '@oula/oula';
import type {
  FeedbackPersonResult,
  IQueryFeedbackPerson,
} from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type {
  DepartmentInfoItem,
  IQueryDepartmentInfo,
} from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type {
  DictResult,
  IQueryFeedbackTagsAndAgentDict,
} from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type {
  ConversationList,
  IQueryConversationList,
} from '@/services/types/api/conversationRecord/queryConversationList';
import type {
  ConversationStaticsResult,
  IQueryConversationStatics,
} from '@/services/types/api/conversationRecord/queryConversationStatics';
import type {
  QuestionList,
  IQueryQuestionList,
} from '@/services/types/api/conversationRecord/queryQuestionList';
import type {
  QuestionStaticsResult,
  IQueryQuestionStatics,
} from '@/services/types/api/conversationRecord/queryQuestionStatics';
import type {
  IDownloadQuestionList,
} from '@/services/types/api/conversationRecord/downloadQuestionList';

import { queryDepartmentInfo, queryFeedbackTagsAndAgentDict, queryFeedbackPerson } from '@/services/dataFeedback';
import {
  queryConversationList,
  queryQuestionList,
  downloadQuestionList,
  queryConversationStatics,
  queryQuestionStatics,
  querySubQuestionList,
  downloadSubQuestionList,
  queryDict,
  querySubQuestionStatics,
} from '@/services/conversationRecord';

export interface ConversationRecordModelState {
  /** 会话维度提问人 */
  conversationQuestioner: FeedbackPersonResult;
  /** 问题维度提问人 */
  questionQuestioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
   /** 反馈标签和Agent字典 */
  dictData: DictResult;
  /** 答案来源 */
    dictAnsourceData: DictResult;
  /** 会话维度列表 */
  conversationList: ConversationList;
  /** 会话维度的统计信息 */
  conversationStatics: ConversationStaticsResult;
  /** 问题维度列表 */
  questionList: QuestionList;
   /** 问题维度的统计信息 */
  questionStatics: QuestionStaticsResult;
  /** 问题维度列表 */
  subQuestionList: QuestionList;
/** 子会话维度的统计信息 */
  subQuestionStatics: ConversationStaticsResult
}

export interface ConversationRecordModelType {
  namespace: string;
  state: ConversationRecordModelState;
  reducers: {
    queryConversationQuestionerSuccess: Reducer<ConversationRecordModelState>;
    queryQuestionQuestionerSuccess: Reducer<ConversationRecordModelState>;
    queryDepartmentInfoSuccess: Reducer<ConversationRecordModelState>;
    queryFeedbackTagsAndAgentDictSuccess: Reducer<ConversationRecordModelState>;
    queryConversationListSuccess: Reducer<ConversationRecordModelState>;
    queryConversationStaticsSuccess: Reducer<ConversationRecordModelState>;
    queryQuestionListSuccess: Reducer<ConversationRecordModelState>;
    queryQuestionStaticsSuccess: Reducer<ConversationRecordModelState>;
    querySubQuestionListSuccess: Reducer<ConversationRecordModelState>;
    querySubQuestionStaticsSuccess: Reducer<ConversationRecordModelState>;
    queryAnswersSourcesDictSuccess: Reducer<ConversationRecordModelState>;
  };
  effects: {
    queryConversationQuestioner: Effect;
    queryQuestionQuestioner: Effect;
    queryDepartmentInfo: Effect;
    queryFeedbackTagsAndAgentDict: Effect;
    queryConversationList: Effect;
    queryConversationStatics: Effect;
    queryQuestionList: Effect;
    queryQuestionStatics: Effect;
    downloadQuestionList: Effect;
    querySubQuestionList: Effect;
    downloadSubQuestionList: Effect;
    querySubQuestionStatics: Effect;
    queryAnswersSourcesDict: Effect;
  };
}

const ConversationRecordModel: ConversationRecordModelType = {
  namespace: 'conversationRecord',
  state: {
    /** 会话维度提问人 */
    conversationQuestioner: {} as FeedbackPersonResult,
    /** 问题维度提问人 */
    questionQuestioner: {} as FeedbackPersonResult,
    /** 所属部门 */
    department: [],
    /** 反馈标签和Agent字典 */
    dictData: {} as DictResult,
    /** 答案来源 */
    dictAnsourceData: {} as DictResult,
    /** 会话维度列表 */
    conversationList: {} as ConversationList,
    /** 会话维度的统计信息 */
    conversationStatics: {} as ConversationStaticsResult,
    /** 问题维度列表 */
    questionList: {} as QuestionList,
    /** 会话维度的统计信息 */
    questionStatics: {} as QuestionStaticsResult,
    /** 子问题维度列表 */
    subQuestionList: {} as QuestionList,
    /** 子会话维度的统计信息 */
    subQuestionStatics:{} as QuestionStaticsResult,

  },
  effects: {
    // 查询会话维度提问人(同查询反馈人接口)
    *queryConversationQuestioner({ payload }, { call, put }) {
      const response: IQueryFeedbackPerson['Response'] = yield call(queryFeedbackPerson, payload);
      yield put({
        type: 'queryConversationQuestionerSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询问题维度提问人(同查询反馈人接口)
    *queryQuestionQuestioner({ payload }, { call, put }) {
      const response: IQueryFeedbackPerson['Response'] = yield call(queryFeedbackPerson, payload);
      yield put({
        type: 'queryQuestionQuestionerSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询所属部门
    *queryDepartmentInfo({ payload }, { call, put }) {
      const response: IQueryDepartmentInfo['Response'] = yield call(queryDepartmentInfo, payload);
      yield put({
        type: 'queryDepartmentInfoSuccess',
        payload: response?.resultData || [],
      });
    },
    // 查询反馈标签和Agent字典
    *queryFeedbackTagsAndAgentDict({ payload }, { call, put }) {
      const response: IQueryDepartmentInfo['Response'] = yield call(queryFeedbackTagsAndAgentDict, payload);
      yield put({
        type: 'queryFeedbackTagsAndAgentDictSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询答案来源字典
    *queryAnswersSourcesDict({ payload }, { call, put }) {
      const response: IQueryDepartmentInfo['Response'] = yield call(queryDict, payload);
      yield put({
        type: 'queryAnswersSourcesDictSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询会话维度列表
    *queryConversationList({ payload }, { call, put }) {
      const response: IQueryConversationList['Response'] = yield call(queryConversationList, payload);
      yield put({
        type: 'queryConversationListSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询会话维度的统计信息
    *queryConversationStatics({ payload }, { call, put }) {
      const response: IQueryConversationStatics['Response'] = yield call(queryConversationStatics, payload);
      yield put({
        type: 'queryConversationStaticsSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询问题维度列表
    *queryQuestionList({ payload }, { call, put }) {
      const response: IQueryQuestionList['Response'] = yield call(queryQuestionList, payload);
      yield put({
        type: 'queryQuestionListSuccess',
        payload: response?.resultData || {},
      });
    },
    // 查询问题维度的统计信息
    *queryQuestionStatics({ payload }, { call, put }) {
      const response: IQueryQuestionStatics['Response'] = yield call(queryQuestionStatics, payload);
      yield put({
        type: 'queryQuestionStaticsSuccess',
        payload: response?.resultData || {},
      });
    },
    // 下载问题维度列表
    *downloadQuestionList({ payload }, { call }) {
      const response: IDownloadQuestionList['Response'] = yield call(downloadQuestionList, payload);
      return response?.resultData || null;
    },
    // 查询子问题维度列表
    *querySubQuestionList({ payload }, { call, put }) {
      const response: IQueryQuestionList['Response'] = yield call(querySubQuestionList, payload);
      yield put({
        type: 'querySubQuestionListSuccess',
        payload: response?.resultData || {},
      });
    },
    // 下载子问题维度列表
    *downloadSubQuestionList({ payload }, { call }) {
      const response: IDownloadQuestionList['Response'] = yield call(downloadQuestionList, payload);
      return response?.resultData || null;
    },
    // 查询问题维度的统计信息
    *querySubQuestionStatics({ payload }, { call, put }) {
      const response: IQueryQuestionStatics['Response'] = yield call(querySubQuestionStatics, payload);
      yield put({
        type: 'querySubQuestionStaticsSuccess',
        payload: response?.resultData || {},
      });
    },
  },
  reducers: {
    queryConversationQuestionerSuccess(state, action) {
      return {
        ...state,
        conversationQuestioner: action?.payload,
      };
    },
    queryQuestionQuestionerSuccess(state, action) {
      return {
        ...state,
        questionQuestioner: action?.payload,
      };
    },
    queryDepartmentInfoSuccess(state, action) {
      return {
        ...state,
        department: action?.payload,
      };
    },
    queryFeedbackTagsAndAgentDictSuccess(state, action) {
      return {
        ...state,
        dictData: action?.payload,
      };
    },
    queryAnswersSourcesDictSuccess(state, action) {
      return {
        ...state,
        dictAnsourceData: action?.payload,
      };
    },
    queryConversationListSuccess(state, action) {
      return {
        ...state,
        conversationList: action?.payload,
      };
    },
    queryConversationStaticsSuccess(state, action) {
      return {
        ...state,
        conversationStatics: action?.payload,
      };
    },
    queryQuestionListSuccess(state, action) {
      return {
        ...state,
        questionList: action?.payload,
      };
    },
    queryQuestionStaticsSuccess(state, action) {
      return {
        ...state,
        questionStatics: action?.payload,
      };
    },
    querySubQuestionListSuccess(state, action) {
      return {
        ...state,
        subQuestionList: action?.payload,
      };
    },
    querySubQuestionStaticsSuccess(state, action) {
      return {
        ...state,
        subQuestionStatics: action?.payload,
      };
    },
  },
};

export default ConversationRecordModel;
