/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable @typescript-eslint/no-extraneous-class */
/**
 * 文件下载 返回blob
 */
import axios from 'axios';
import { message } from '@ht/sprite-ui';

const api = axios.create({
  // 请求前缀
  baseURL: '',
  // 发送请求时需带上cookie
  withCredentials: true,
  // 响应类型
  responseType: 'blob',
});

// 拦截响应
api.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      const { data } = response;
      if (data) {
        return Promise.resolve(response);
      }
    }
    message.error('请求服务错误');
    return Promise.reject(response);
  },
  (error) => {
    message.error('网络错误');
    return Promise.reject(error);
  },
);

export class Http {
  static get(url: string, config = {}) {
    return new Promise((resolve, reject) => {
      api
        .get(url, config)
        .then((res) => resolve(res))
        .catch((err) => reject(err));
    });
  }

  static post(url: string, params = {}, config = {}) {
    return new Promise((resolve, reject) => {
      api
        .post(url, { ...params }, config)
        .then((res) => resolve(res))
        .catch((err) => reject(err));
    });
  }
};

/**
 * 导出get，post通用方法
 * @param {Object} res - 接口回参
 * @param {String} fileName - 下载文件的名称，可选，
 * 如果传名称，优先选择传的名字，必须要加文件名后缀
 * 没传的情况下，则使用 content-disposition: attachment;filename=xxx.xls
 * 注意：如果调用方法时，没有传递名称，点击下载文件名称为 undefined，则说明 headers里面也没有数据，这个时候需要 传递名称
 */
function exportFileCommon(res: any, fileName: string, resolve: any, reject: any) {
  if (res?.data?.type === 'text/html') {
    reject(null);
    return;
  }

  if (res?.data?.type === 'application/json') {
    const file = new FileReader(); // 读取文件
    file.readAsText(res.data, 'utf-8'); // 读取文件，并设置编码格式为utf-8
    file.onload = () => { // 在读取文件操作完成后触发
      try {
        const result = JSON.parse(file.result as string); // reader.result返回文件的内容，只在读取操作完成后有效
        resolve(result);
      } catch (error) {
        reject(null);
      }
    };
    file.onerror = () => {
      reject(null);
    };
    return;
  }

  // 文件名，如果没传，则从headers里获取 文件名, 并转义
  const tempFileName = fileName
    || decodeURI(
      res?.headers?.['content-disposition']?.match(/filename=(.*)/i)?.[1],
    );

  const { data: blob } = res;
  if (blob && blob.size !== 0) {
    const eleLink = document.createElement('a');
    if (window?.navigator?.msSaveOrOpenBlob) {
      window.navigator.msSaveOrOpenBlob(blob, tempFileName);
    } else {
      document.body.appendChild(eleLink);
      eleLink.download = tempFileName;
      eleLink.style.display = 'none';
      eleLink.href = URL.createObjectURL(blob);
      eleLink.click();
      document.body.removeChild(eleLink);
      URL.revokeObjectURL(eleLink.href);
    }
  }
  resolve(blob);
}

/**
 * 用于使用post方式导出或下载
 * @param {String} url - 导出或下载地址
 * @param {Object} params - 入参，可选
 * @param {String} fileName - 下载文件的名称，可选，
 * @example
 *  exportFileByPost({
      url: DOWNFAIL_URL,
      params: { errorList: data },
      fileName: '提交失败信息.xls',
      config: {},
    });
 */
function exportFileByPost({ url, params, fileName, config }: any) {
  return new Promise((resolve, reject) => Http.post(url, params, config).then((res) => {
    exportFileCommon(res, fileName, resolve, reject);
  }).catch(() => {
    reject();
  }));
}

/**
 * 用于使用get方式导出或下载
 * @param {String} url - 导出或下载地址
 * @param {String} fileName - 下载文件的名称，可选，
 * @example
 *  exportFileByGet({
      url: DOWNFAIL_URL,
      fileName: '提交失败信息.xls',
      config: {},
    });
 */
function exportFileByGet({ url, fileName, config }: any) {
  return new Promise((resolve, reject) => Http.get(url, config).then((res) => {
    exportFileCommon(res, fileName, resolve, reject);
  }).catch(() => {
    reject();
  }));
}

export { exportFileByPost, exportFileByGet };
