/**
 * 生成dva的effects，用于接口数据调用
 * 用于取代 以前 getDataFunction 函数
 * @param {String} type - dva的dispatch action的type值
 *  格式： '${namespace}/${effectsGeneratorFunctionName}' ,例子 'app/querEmpInfo'
 * @param {Object} [options={}] - 接口调用时候的配置对象 可选
 * @param {Boolean} [options.loading=true] - 是否全局显示Loading遮罩，默认为true, 可选
 * @return {Function} dispatch function
 *
 * @example
 *  generateEffect('app/getEmpInfo', { loading: true });
 */
export function generateEffect(type: any, options = {}) {
  const action = { loading: true };
  return (query: any) => ({
    payload: query || {},
    type,
    ...action,
    ...options,
  });
}
