# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist


# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
package-lock.json
*bak

# visual studio code
.history
*.log
functions/*
.temp/**

# umi
.umi
.umi-production

# oula
.oula
.oula-production

# screenshot
screenshot
.firebase
.eslintcache

build

eslint-error.html
