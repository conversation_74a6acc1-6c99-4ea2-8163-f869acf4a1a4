module.exports = {
  extends: ['@ht/eslint-config-htsc/react'],
  'settings': {
    'import/core-modules': ['aorta-common'],
  },
  rules: {
    'prettier/prettier': 'off',
    semi: 'error',
    'space-in-parens': ['error', 'never'],
    'comma-spacing': ['error', { 'before': false, 'after': true }],
    'space-unary-ops': [ 'error', { words: true, nonwords: false } ],
    'space-infix-ops': 'error',
    'block-spacing': ['error', 'always'],
    'object-curly-spacing': ['error', 'always'],
    'max-lines': 'off',
    'max-statements': 'off',
    'no-trailing-spaces': ['error'],
    'eol-last': ['error'],
    eqeqeq: ['error'],
    'comma-dangle': ['error', 'always-multiline'],
    quotes: ['error', 'single'],
    'no-plusplus': 'off',
    'react/no-unused-prop-types': 'warn',
    'no-undef':'error',
    'no-mixed-operators': 'warn',
    'import/no-anonymous-default-export': 'off',
    'import/no-named-as-default-member': 'off',
    'max-len': ['warn', { code: 120, tabWidth: 2 }],
    'no-extra-parens': [
      'error',
      'all',
      { ignoreJSX: 'all' },
    ],
    'react/sort-comp': 'off',
    'react/boolean-prop-naming': 'off',
    'react/jsx-wrap-multilines': [
      'error',
      {
        declaration: 'parens-new-line',
        assignment: 'parens-new-line',
        return: 'parens',
        arrow: 'parens-new-line',
        condition: 'parens-new-line',
        logical: 'parens-new-line',
        prop: 'parens-new-line',
      },
    ],
    'react/jsx-indent': ['error', 2],
    'react/jsx-indent-props': ['error', 2],
    indent: [
      'error',
      2,
      {
        SwitchCase: 1,
        VariableDeclarator: 'first',
        outerIIFEBody: 0,
        MemberExpression: 1,
        FunctionDeclaration: { parameters: 'first' },
        FunctionExpression: { parameters: 'first' },
        CallExpression: { arguments: 'first' },
        ArrayExpression: 1,
        ObjectExpression: 1,
        ImportDeclaration: 1,
        flatTernaryExpressions: false,
      },
    ],
    '@typescript-eslint/consistent-type-imports': 'error',
    'import/order': 'off',
    'import/first': 'off',
    'import/imports-first': 'off',
  },
  'globals': {
    'ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION': true,
    'page': true,
    'REACT_APP_ENV': true,
  },
};
